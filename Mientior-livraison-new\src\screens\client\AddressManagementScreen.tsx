import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  StatusBar,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { Address, AddressType } from '../../types';

const AddressManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    userAddresses,
    defaultAddress,
    loading,
    loadUserAddresses,
    deleteAddress,
    setDefaultAddress,
  } = useAuthStore();
  
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadUserAddresses();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadUserAddresses();
    } catch (error) {
      console.error('Erreur refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleDeleteAddress = (address: Address) => {
    Alert.alert(
      'Supprimer l\'adresse',
      `Êtes-vous sûr de vouloir supprimer "${address.label}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAddress(address.id);
              Alert.alert('Succès', 'Adresse supprimée avec succès');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de supprimer l\'adresse');
            }
          },
        },
      ]
    );
  };

  const handleSetDefault = async (address: Address) => {
    if (address.is_default) return;
    
    try {
      await setDefaultAddress(address.id);
      Alert.alert('Succès', 'Adresse par défaut mise à jour');
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de définir l\'adresse par défaut');
    }
  };

  const getAddressTypeIcon = (type: AddressType) => {
    switch (type) {
      case 'home':
        return 'home';
      case 'work':
        return 'business';
      case 'other':
        return 'location';
      default:
        return 'location';
    }
  };

  const getAddressTypeLabel = (type: AddressType) => {
    switch (type) {
      case 'home':
        return 'Domicile';
      case 'work':
        return 'Travail';
      case 'other':
        return 'Autre';
      default:
        return 'Adresse';
    }
  };

  const renderAddressItem = ({ item }: { item: Address }) => (
    <View style={styles.addressItem}>
      <View style={styles.addressHeader}>
        <View style={styles.addressTypeContainer}>
          <Ionicons 
            name={getAddressTypeIcon(item.label)} 
            size={20} 
            color="#0DCAA8" 
          />
          <Text style={styles.addressType}>
            {getAddressTypeLabel(item.label)}
          </Text>
          {item.is_default && (
            <View style={styles.defaultBadge}>
              <Text style={styles.defaultText}>Par défaut</Text>
            </View>
          )}
        </View>
        
        <View style={styles.addressActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleSetDefault(item)}
            disabled={item.is_default}
          >
            <Ionicons 
              name={item.is_default ? "star" : "star-outline"} 
              size={20} 
              color={item.is_default ? "#FFD700" : "#666"} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteAddress(item)}
          >
            <Ionicons name="trash-outline" size={20} color="#FF4444" />
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.addressName}>{item.nom_complet}</Text>
      <Text style={styles.addressText}>
        {item.adresse_ligne1}
        {item.adresse_ligne2 ? `, ${item.adresse_ligne2}` : ''}
      </Text>
      <Text style={styles.addressLocation}>
        {item.quartier}, {item.ville}
        {item.code_postal ? ` ${item.code_postal}` : ''}
      </Text>
      
      {item.instructions_livraison && (
        <Text style={styles.deliveryInstructions}>
          📝 {item.instructions_livraison}
        </Text>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#0DCAA8" barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mes Adresses</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            // TODO: Navigate to AddAddressScreen
            Alert.alert('Info', 'Écran d\'ajout d\'adresse à implémenter');
          }}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {userAddresses.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="location-outline" size={64} color="#CCC" />
            <Text style={styles.emptyTitle}>Aucune adresse</Text>
            <Text style={styles.emptyText}>
              Ajoutez votre première adresse pour faciliter vos commandes
            </Text>
            <TouchableOpacity
              style={styles.addFirstButton}
              onPress={() => {
                Alert.alert('Info', 'Écran d\'ajout d\'adresse à implémenter');
              }}
            >
              <Text style={styles.addFirstButtonText}>Ajouter une adresse</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={userAddresses}
            renderItem={renderAddressItem}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0DCAA8']}
              />
            }
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#0DCAA8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  addButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  addressItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addressTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  addressType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  defaultBadge: {
    backgroundColor: '#0DCAA8',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  defaultText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  addressActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  addressName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  addressLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  deliveryInstructions: {
    fontSize: 12,
    color: '#0DCAA8',
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  addFirstButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  addFirstButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default AddressManagementScreen;
