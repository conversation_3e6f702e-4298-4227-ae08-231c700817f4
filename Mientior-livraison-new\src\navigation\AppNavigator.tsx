import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../store/authStore';
import { RootStackParamList } from '../types';

// Debug utilities
import { InitializationDebugger, PerformanceMonitor, withTimeoutAndLogging } from '../utils/debugInitialization';

// Écrans d'authentification et onboarding
import LoadingScreen from '../screens/LoadingScreen';
import { OnboardingCarouselScreen } from '../screens/client/OnboardingCarouselScreen';
import LanguageSelectionScreen from '../screens/auth/LanguageSelectionScreen';
import LocationPermissionScreen from '../screens/auth/LocationPermissionScreen';
import { AuthChoiceScreen } from '../screens/client/AuthChoiceScreen';
import SignInScreen from '../screens/client/SignInScreen';
import SignUpScreen from '../screens/client/SignUpScreen';
import OTPVerificationScreen from '../screens/client/OTPVerificationScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';
import SupabaseTestScreen from '../screens/SupabaseTestScreen';

// Navigateurs par rôle
import ClientNavigator from './ClientNavigator';
import DeliveryNavigator from './DeliveryNavigator';
import MerchantNavigator from './MerchantNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  console.log('🚀🚀🚀 APPNAVIGATOR DÉMARRÉ - DÉBUT DU COMPOSANT 🚀🚀🚀');

  const {
    user,
    isAuthenticated,
    initialize,
    loadUserAddresses,
    setCurrentLocation,
    setLocationPermission
  } = useAuthStore();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [initializationComplete, setInitializationComplete] = useState(false);
  const [minSplashTimeComplete, setMinSplashTimeComplete] = useState(false);

  console.log('📊 États initiaux:', { isLoading, initializationComplete, minSplashTimeComplete, isFirstTime });



  // Délai minimum pour le SplashScreen (éviter le clignotement)
  useEffect(() => {
    InitializationDebugger.log('🕐 Démarrage timer splash minimum (2s)');
    PerformanceMonitor.checkpoint('splash_timer_start');

    const minSplashTimer = setTimeout(() => {
      InitializationDebugger.log('✅ Timer splash minimum terminé');
      PerformanceMonitor.checkpoint('splash_timer_complete');
      setMinSplashTimeComplete(true);
    }, 2000); // 2 secondes minimum

    return () => clearTimeout(minSplashTimer);
  }, []);

  // Timeout de sécurité pour éviter le chargement infini - RÉDUIT À 8 SECONDES
  useEffect(() => {
    InitializationDebugger.log('⏰ Démarrage timeout de sécurité (8s)');
    PerformanceMonitor.checkpoint('security_timeout_start');

    const timeout = setTimeout(() => {
      InitializationDebugger.warning('TIMEOUT DE SÉCURITÉ ACTIVÉ - Forcer la fin du chargement après 8 secondes');
      InitializationDebugger.log('📊 État actuel', {
        initializationComplete,
        minSplashTimeComplete,
        isFirstTime,
        isLoading
      });

      PerformanceMonitor.checkpoint('security_timeout_triggered');

      // Mode de récupération d'urgence
      setInitializationComplete(true);
      setMinSplashTimeComplete(true);
      if (isFirstTime === null) {
        InitializationDebugger.warning('🚨 Mode de récupération: Définir isFirstTime = true');
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      }

      // Exporter les logs pour débogage
      console.log('\n' + InitializationDebugger.exportLogs());
      console.log('\n' + PerformanceMonitor.getReport());
    }, 8000); // 8 secondes maximum au lieu de 15

    return () => clearTimeout(timeout);
  }, []); // Supprimer la dépendance isFirstTime pour éviter les re-créations

  // Initialiser l'application et charger les données utilisateur
  useEffect(() => {
    let isMounted = true; // Éviter les mises à jour si le composant est démonté

    const initializeApp = async () => {
      try {
        InitializationDebugger.log('🚀 Début initialisation app');
        PerformanceMonitor.checkpoint('app_init_start');

        // Utiliser withTimeoutAndLogging pour l'initialisation auth
        try {
          await withTimeoutAndLogging(
            () => initialize(),
            'Initialisation authentification',
            6000 // 6 secondes timeout
          );
          PerformanceMonitor.checkpoint('auth_init_complete');
        } catch (initError) {
          InitializationDebugger.error('Erreur initialisation auth, continuer quand même', initError);
          // Ne pas bloquer l'app si l'auth échoue
        }

        // Vérifier si c'est la première fois avec timeout
        try {
          const hasSeenOnboarding = await Promise.race([
            AsyncStorage.getItem('hasSeenOnboarding'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage onboarding')), 2000)
            )
          ]);
          if (isMounted) {
            setIsFirstTime(hasSeenOnboarding === null);
            console.log('📱 Premier lancement:', hasSeenOnboarding === null);
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture onboarding, défaut = true:', error);
          if (isMounted) {
            setIsFirstTime(true); // Par défaut si erreur
          }
        }

        // Charger les données de localisation sauvegardées avec timeout
        try {
          const locationPermission = await Promise.race([
            AsyncStorage.getItem('locationPermissionGranted'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage location')), 2000)
            )
          ]);
          if (locationPermission && isMounted) {
            setLocationPermission(locationPermission === 'true');
            console.log('📍 Permission localisation:', locationPermission);
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture permission localisation:', error);
        }

        try {
          const lastLocation = await Promise.race([
            AsyncStorage.getItem('lastKnownLocation'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout AsyncStorage lastLocation')), 2000)
            )
          ]) as string | null;
          if (lastLocation && isMounted) {
            try {
              const location = JSON.parse(lastLocation);
              setCurrentLocation(location);
              console.log('🗺️ Dernière position chargée');
            } catch (parseError) {
              console.error('Erreur parsing location:', parseError);
            }
          }
        } catch (error) {
          console.error('⚠️ Erreur lecture dernière localisation:', error);
        }

      } catch (error) {
        console.error('❌ Erreur initialisation app:', error);
        if (isMounted) {
          setIsFirstTime(true); // Par défaut, montrer l'onboarding
        }
      } finally {
        console.log('🏁 Fin initialisation app');
        if (isMounted) {
          setInitializationComplete(true);
        }
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Gérer la fin du chargement quand l'initialisation ET le délai minimum sont terminés
  useEffect(() => {
    if (initializationComplete && minSplashTimeComplete) {
      console.log('✅ Conditions remplies - fin du chargement');
      setIsLoading(false);
    }
  }, [initializationComplete, minSplashTimeComplete]);

  // Charger les adresses utilisateur quand l'utilisateur est authentifié
  useEffect(() => {
    if (isAuthenticated && user && user.role) {
      loadUserAddresses().catch(error => {
        console.error('Erreur chargement adresses:', error);
      });
    }
  }, [isAuthenticated, user?.id, user?.role, loadUserAddresses]);

  // BYPASS ULTRA RAPIDE - Forcer le passage après 1 seconde
  const [forceBypass, setForceBypass] = useState(false);

  useEffect(() => {
    console.log('🔥 DÉMARRAGE BYPASS ULTRA RAPIDE - 1 seconde');
    const bypassTimer = setTimeout(() => {
      console.log('🚨🚨🚨 BYPASS ACTIVÉ - FORCER LE PASSAGE MAINTENANT 🚨🚨🚨');
      console.log('🎯 Définition des états pour forcer la navigation');
      setForceBypass(true);
      setInitializationComplete(true);
      setMinSplashTimeComplete(true);
      setIsLoading(false);
      if (isFirstTime === null) {
        console.log('📱 Première fois détectée - Direction onboarding');
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      }
      console.log('✅ BYPASS TERMINÉ - L\'app devrait naviguer maintenant');
    }, 1000); // 1 seconde seulement

    return () => clearTimeout(bypassTimer);
  }, []);

  // DÉSACTIVATION TEMPORAIRE DE L'ÉCRAN DE CHARGEMENT
  // Forcer l'application à naviguer directement vers l'onboarding
  console.log('🚨 BYPASS TOTAL ACTIVÉ - IGNORER ÉCRAN DE CHARGEMENT 🚨');

  // Forcer les valeurs par défaut pour débloquer l'app
  const forcedIsFirstTime = isFirstTime === null ? true : isFirstTime;

  // COMMENTÉ TEMPORAIREMENT - Écran de chargement désactivé
  /*
  if ((isLoading || !initializationComplete || !minSplashTimeComplete || isFirstTime === null) && !forceBypass) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Loading" component={LoadingScreen} />
      </Stack.Navigator>
    );
  }
  */

  // Si l'utilisateur n'est pas authentifié
  if (!isAuthenticated || !user) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
        initialRouteName={forcedIsFirstTime ? "Onboarding" : "AuthChoiceScreen"}
      >
        {/* Workflow d'onboarding complet */}
        <Stack.Screen name="Onboarding" component={OnboardingCarouselScreen} />
        <Stack.Screen name="LanguageSelection" component={LanguageSelectionScreen} />
        <Stack.Screen name="LocationPermission" component={LocationPermissionScreen} />

        {/* Écrans d'authentification */}
        <Stack.Screen name="AuthChoiceScreen" component={AuthChoiceScreen} />
        <Stack.Screen name="SignInScreen" component={SignInScreen} />
        <Stack.Screen name="SignUpScreen" component={SignUpScreen} />
        <Stack.Screen name="ForgotPasswordScreen" component={ForgotPasswordScreen} />
        <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} />
        <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />

        {/* Écran de test */}
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur est authentifié mais n'a pas de rôle défini
  if (!user.role) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Navigateur selon le rôle utilisateur
  const getRoleNavigator = () => {
    switch (user.role) {
      case 'client':
        return ClientNavigator;
      case 'livreur':
        return DeliveryNavigator;
      case 'marchand':
        return MerchantNavigator;
      default:
        return ClientNavigator; // Par défaut, interface client
    }
  };

  const RoleNavigator = getRoleNavigator();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen name="Main" component={RoleNavigator} />
      <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
