import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
  Animated,
  Alert,
  KeyboardAvoidingView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, shadows } from '../../constants/theme';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../../components/ui/Button';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FormData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  password: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  password?: string;
  general?: string;
}

const SignUpScreen: React.FC = () => {
  const navigation = useNavigation();
  const { signUp, loading } = useAuth();

  // État du formulaire
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptNewsletter, setAcceptNewsletter] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{ score: number; label: string; color: string }>({
    score: 0,
    label: '',
    color: colors.neutral[300],
  });
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Logo pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Icon rotation animation
    Animated.loop(
      Animated.timing(iconRotateAnim, {
        toValue: 1,
        duration: 10000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  // Validation du formulaire
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Validation du prénom
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
      isValid = false;
    } else if (!/^[a-zA-ZÀ-ÿ\s'-]{2,30}$/.test(formData.firstName.trim())) {
      newErrors.firstName = 'Prénom invalide';
      isValid = false;
    }

    // Validation du nom
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
      isValid = false;
    } else if (!/^[a-zA-ZÀ-ÿ\s'-]{2,30}$/.test(formData.lastName.trim())) {
      newErrors.lastName = 'Nom invalide';
      isValid = false;
    }

    // Validation du téléphone
    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
      isValid = false;
    } else if (!/^\+?[0-9]{8,15}$/.test(formData.phone.trim().replace(/\s/g, ''))) {
      newErrors.phone = 'Numéro de téléphone invalide';
      isValid = false;
    }

    // Validation de l'email
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Email invalide';
      isValid = false;
    }

    // Validation du mot de passe
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
      isValid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Calcul de la force du mot de passe
  const calculatePasswordStrength = (password: string) => {
    if (!password) {
      setPasswordStrength({ score: 0, label: '', color: colors.neutral[300] });
      return;
    }

    let score = 0;
    let label = '';
    let color = '';

    // Longueur
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Complexité
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;

    // Déterminer le niveau
    if (score <= 2) {
      label = 'Mot de passe de force faible';
      color = colors.error;
    } else if (score <= 4) {
      label = 'Mot de passe de force moyenne';
      color = colors.warning;
    } else {
      label = 'Mot de passe de force élevée';
      color = colors.success;
    }

    setPasswordStrength({ score, label, color });
  };

  // Gestion des changements de champs
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    if (field === 'password') {
      calculatePasswordStrength(value);
    }
  };

  // Gestion des champs touchés
  const handleInputBlur = () => {
    validateForm();
  };

  // Soumission du formulaire
  const handleSignUp = async () => {
    if (!validateForm()) {
      return;
    }

    if (!acceptTerms) {
      setErrors(prev => ({
        ...prev,
        general: 'Vous devez accepter les conditions d\'utilisation',
      }));
      return;
    }

    try {
      await signUp(
        formData.email.trim(),
        formData.password,
        {
          full_name: `${formData.firstName.trim()} ${formData.lastName.trim()}`,
          phone: formData.phone.trim(),
        }
      );

      Alert.alert(
        'Compte créé avec succès',
        'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.',
        [{ text: 'OK', onPress: () => navigation.navigate('SignInScreen' as never) }]
      );
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        general: 'Erreur lors de la création du compte. Veuillez réessayer.',
      }));
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header with back button and logo */}
          <Animated.View style={[
            styles.header,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
              accessibilityLabel="Retour"
              accessibilityRole="button"
            >
              <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
            </TouchableOpacity>

            <View style={styles.logoContainer}>
              <Animated.View style={[
                styles.logo,
                {
                  transform: [
                    { scale: pulseAnim },
                    { rotate: iconRotateAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })}
                  ]
                }
              ]}>
                <Ionicons name="flame" size={24} color="#FFFFFF" />
              </Animated.View>
            </View>
          </Animated.View>

          {/* Title and subtitle */}
          <Animated.View style={[
            styles.titleContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}>
            <Text style={styles.title}>Créer un compte</Text>
            <Text style={styles.subtitle}>Remplissez vos informations pour créer votre compte</Text>
          </Animated.View>

          {/* Form fields */}
          <Animated.View style={[
            styles.form,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}>
            {/* Last Name Input */}
            <Animated.View style={[
              styles.inputContainer,
              {
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { scale: scaleAnim }
                ]
              }
            ]}>
              <View style={styles.inputWrapper}>
                <View style={styles.inputIconContainer}>
                  <Ionicons name="person" size={20} color="#0DCAA8" />
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Nom"
                  placeholderTextColor={colors.text.secondary}
                  value={formData.lastName}
                  onChangeText={(value) => handleInputChange('lastName', value)}
                  onBlur={handleInputBlur}
                  autoCapitalize="words"
                  autoCorrect={false}
                />
              </View>
              {errors.lastName && (
                <Animated.Text style={styles.errorMessage}>{errors.lastName}</Animated.Text>
              )}
            </Animated.View>

            {/* First Name Input */}
            <Animated.View style={[
              styles.inputContainer,
              {
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { scale: scaleAnim }
                ]
              }
            ]}>
              <View style={styles.inputWrapper}>
                <View style={styles.inputIconContainer}>
                  <Ionicons name="person" size={20} color="#0DCAA8" />
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Prénom"
                  placeholderTextColor={colors.text.secondary}
                  value={formData.firstName}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  onBlur={handleInputBlur}
                  autoCapitalize="words"
                  autoCorrect={false}
                />
              </View>
              {errors.firstName && (
                <Animated.Text style={styles.errorMessage}>{errors.firstName}</Animated.Text>
              )}
            </Animated.View>

            {/* Phone Input */}
            <Animated.View style={[
              styles.inputContainer,
              {
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { scale: scaleAnim }
                ]
              }
            ]}>
              <View style={styles.inputWrapper}>
                <View style={styles.inputIconContainer}>
                  <Ionicons name="call" size={20} color="#0DCAA8" />
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Numéro de téléphone"
                  placeholderTextColor={colors.text.secondary}
                  value={formData.phone}
                  onChangeText={(value) => handleInputChange('phone', value)}
                  onBlur={handleInputBlur}
                  keyboardType="phone-pad"
                  autoCorrect={false}
                />
              </View>
              {errors.phone && (
                <Animated.Text style={styles.errorMessage}>{errors.phone}</Animated.Text>
              )}
            </Animated.View>

            {/* Email Input */}
            <Animated.View style={[
              styles.inputContainer,
              {
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { scale: scaleAnim }
                ]
              }
            ]}>
              <View style={styles.inputWrapper}>
                <View style={styles.inputIconContainer}>
                  <Ionicons name="mail" size={20} color="#0DCAA8" />
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Email"
                  placeholderTextColor={colors.text.secondary}
                  value={formData.email}
                  onChangeText={(value) => handleInputChange('email', value)}
                  onBlur={handleInputBlur}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
              {errors.email && (
                <Animated.Text style={styles.errorMessage}>{errors.email}</Animated.Text>
              )}
            </Animated.View>

            {/* Password Input */}
            <Animated.View style={[
              styles.inputContainer,
              {
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { scale: scaleAnim }
                ]
              }
            ]}>
              <View style={styles.inputWrapper}>
                <View style={styles.inputIconContainer}>
                  <Ionicons name="lock-closed" size={20} color="#0DCAA8" />
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Mot de passe"
                  placeholderTextColor={colors.text.secondary}
                  value={formData.password}
                  onChangeText={(value) => handleInputChange('password', value)}
                  onBlur={handleInputBlur}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-off" : "eye"}
                    size={20}
                    color="#0DCAA8"
                  />
                </TouchableOpacity>
              </View>

              {/* Password Strength Indicator */}
              {formData.password.length > 0 && (
                <Animated.View style={styles.passwordStrengthContainer}>
                  <View style={styles.passwordStrengthBar}>
                    <Animated.View
                      style={[
                        styles.passwordStrengthFill,
                        {
                          width: `${(passwordStrength.score / 7) * 100}%`,
                          backgroundColor: passwordStrength.color
                        }
                      ]}
                    />
                  </View>
                  <Text style={styles.passwordStrengthText}>
                    {passwordStrength.label}
                  </Text>
                </Animated.View>
              )}

              {errors.password && (
                <Animated.Text style={styles.errorMessage}>{errors.password}</Animated.Text>
              )}
            </Animated.View>

            {/* Terms and Newsletter Options */}
            <Animated.View style={[
              styles.optionsContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              {/* Terms Acceptance */}
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setAcceptTerms(!acceptTerms)}
              >
                <Animated.View style={[
                  styles.checkbox,
                  acceptTerms && styles.checkboxChecked,
                  { transform: [{ scale: acceptTerms ? 1.1 : 1 }] }
                ]}>
                  {acceptTerms && (
                    <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                  )}
                </Animated.View>
                <Text style={styles.checkboxText}>
                  J'accepte les conditions générales d'utilisation
                </Text>
              </TouchableOpacity>

              {/* Newsletter Subscription */}
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setAcceptNewsletter(!acceptNewsletter)}
              >
                <Animated.View style={[
                  styles.checkbox,
                  acceptNewsletter && styles.checkboxChecked,
                  { transform: [{ scale: acceptNewsletter ? 1.1 : 1 }] }
                ]}>
                  {acceptNewsletter && (
                    <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                  )}
                </Animated.View>
                <Text style={styles.checkboxText}>
                  Je souhaite recevoir la newsletter
                </Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Sign Up Button */}
            <Animated.View style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }}>
              <TouchableOpacity
                style={[
                  styles.signUpButton,
                  loading && styles.signUpButtonDisabled
                ]}
                onPress={handleSignUp}
                disabled={loading}
                activeOpacity={0.8}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <Animated.View style={[
                      styles.loadingSpinner,
                      { transform: [{ rotate: iconRotateAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg']
                      })}] }
                    ]}>
                      <Ionicons name="refresh" size={20} color="#FFFFFF" />
                    </Animated.View>
                    <Text style={styles.signUpButtonText}>Inscription...</Text>
                  </View>
                ) : (
                  <Text style={styles.signUpButtonText}>S'inscrire</Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            {/* Sign In Link */}
            <Animated.View style={[
              styles.signInContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              <Text style={styles.signInText}>Déjà un compte? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignInScreen' as never)}>
                <Text style={styles.signInLink}>Se connecter</Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Social Login Divider */}
            <Animated.View style={[
              styles.dividerContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              <Text style={styles.dividerText}>ou continuer avec</Text>
            </Animated.View>

            {/* Social Login Buttons */}
            <Animated.View style={[
              styles.socialContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              {/* Google Button */}
              <TouchableOpacity style={styles.socialButton}>
                <Ionicons name="logo-google" size={24} color="#DB4437" />
              </TouchableOpacity>

              {/* Facebook Button */}
              <TouchableOpacity style={styles.socialButton}>
                <Ionicons name="logo-facebook" size={24} color="#4267B2" />
              </TouchableOpacity>

              {/* Apple Button */}
              <TouchableOpacity style={styles.socialButton}>
                <Ionicons name="logo-apple" size={24} color="#000000" />
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>
        </Animated.View>
      </ScrollView>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 28,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Platform.OS === 'ios' ? 50 : 30,
    marginBottom: 20,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    padding: 5,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0DCAA8',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 8,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 17,
    color: '#6B7280',
    textAlign: 'center',
    paddingHorizontal: 24,
    lineHeight: 24,
    fontWeight: '400',
  },
  form: {
    flex: 1,
    paddingTop: 8,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 16,
    paddingVertical: 4,
    minHeight: 56,
  },
  inputIconContainer: {
    marginRight: 12,
    width: 24,
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 16,
    fontWeight: '400',
  },
  passwordToggle: {
    padding: 8,
    marginLeft: 8,
  },
  passwordStrengthContainer: {
    marginTop: 8,
    paddingHorizontal: 4,
  },
  passwordStrengthBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  passwordStrengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  passwordStrengthText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  errorMessage: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
    marginLeft: 4,
    fontWeight: '500',
  },
  optionsContainer: {
    marginVertical: 24,
    paddingHorizontal: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    marginRight: 12,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#0DCAA8',
    borderColor: '#0DCAA8',
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    fontWeight: '400',
  },
  signUpButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    minHeight: 56,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 24,
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  signUpButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  signUpButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingSpinner: {
    marginRight: 8,
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  signInText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '400',
  },
  signInLink: {
    fontSize: 16,
    color: '#0DCAA8',
    fontWeight: '600',
  },
  dividerContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerText: {
    fontSize: 14,
    color: '#9CA3AF',
    fontWeight: '400',
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    gap: 24,
  },
  socialButton: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default SignUpScreen;
