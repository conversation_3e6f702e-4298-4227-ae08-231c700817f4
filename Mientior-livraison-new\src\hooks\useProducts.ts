import { useState, useEffect } from 'react';
import { produitService } from '../services/supabase';
import { Produit } from '../types';
import { safeToLowerCase } from '../utils/stringUtils';

interface ProductsState {
  products: Produit[];
  loading: boolean;
  error: string | null;
  refreshing: boolean;
}

interface ProductFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  available?: boolean;
  search?: string;
}

export const useProducts = (merchantId?: string, filters?: ProductFilters) => {
  const [state, setState] = useState<ProductsState>({
    products: [],
    loading: false,
    error: null,
    refreshing: false,
  });

  useEffect(() => {
    if (merchantId) {
      loadProducts();
    }
  }, [merchantId, filters]);

  const loadProducts = async () => {
    if (!merchantId) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const products = await produitService.getBymerchant(merchantId);
      const filteredProducts = applyFilters(products, filters);
      
      setState(prev => ({ 
        ...prev, 
        products: filteredProducts, 
        loading: false, 
        error: null 
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erreur de chargement des produits',
        loading: false 
      }));
    }
  };

  const refresh = async () => {
    if (!merchantId) return;

    try {
      setState(prev => ({ ...prev, refreshing: true, error: null }));
      const products = await produitService.getBymerchant(merchantId);
      const filteredProducts = applyFilters(products, filters);
      
      setState(prev => ({ 
        ...prev, 
        products: filteredProducts, 
        refreshing: false, 
        error: null 
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erreur de rafraîchissement',
        refreshing: false 
      }));
    }
  };

  const getProductById = async (productId: string): Promise<Produit | null> => {
    try {
      return await produitService.getById(productId);
    } catch (error) {
      console.error('Erreur lors de la récupération du produit:', error);
      return null;
    }
  };

  const searchProducts = (query: string) => {
    if (!query.trim()) {
      return state.products;
    }

    const queryLower = safeToLowerCase(query);
    return state.products.filter(product =>
      safeToLowerCase(product.nom).includes(queryLower) ||
      safeToLowerCase(product.description).includes(queryLower) ||
      safeToLowerCase(product.categorie).includes(queryLower)
    );
  };

  const getProductsByCategory = (category: string) => {
    return state.products.filter(product =>
      safeToLowerCase(product.categorie) === safeToLowerCase(category)
    );
  };

  const getAvailableProducts = () => {
    return state.products.filter(product => product.is_disponible);
  };

  const getProductsInPriceRange = (minPrice: number, maxPrice: number) => {
    return state.products.filter(product => 
      product.prix >= minPrice && product.prix <= maxPrice
    );
  };

  const getCategories = (): string[] => {
    const categories = state.products
      .map(product => product.categorie)
      .filter((category): category is string => !!category);
    
    return Array.from(new Set(categories)).sort();
  };

  const getPriceRange = (): { min: number; max: number } => {
    if (state.products.length === 0) {
      return { min: 0, max: 0 };
    }

    const prices = state.products.map(product => product.prix);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  };

  const applyFilters = (products: Produit[], filters?: ProductFilters): Produit[] => {
    if (!filters) return products;

    return products.filter(product => {
      // Filtre par catégorie
      if (filters.category && product.categorie !== filters.category) {
        return false;
      }

      // Filtre par prix minimum
      if (filters.minPrice !== undefined && product.prix < filters.minPrice) {
        return false;
      }

      // Filtre par prix maximum
      if (filters.maxPrice !== undefined && product.prix > filters.maxPrice) {
        return false;
      }

      // Filtre par disponibilité
      if (filters.available !== undefined && product.is_disponible !== filters.available) {
        return false;
      }

      // Filtre par recherche
      if (filters.search) {
        const searchLower = safeToLowerCase(filters.search);
        const matchesName = safeToLowerCase(product.nom).includes(searchLower);
        const matchesDescription = safeToLowerCase(product.description).includes(searchLower);
        const matchesCategory = safeToLowerCase(product.categorie).includes(searchLower);

        if (!matchesName && !matchesDescription && !matchesCategory) {
          return false;
        }
      }

      return true;
    });
  };

  const getPopularProducts = (limit: number = 10) => {
    return state.products
      .filter(product => product.is_disponible)
      .sort((a, b) => (b.nombre_commandes || 0) - (a.nombre_commandes || 0))
      .slice(0, limit);
  };

  const getRecommendedProducts = (limit: number = 5) => {
    return state.products
      .filter(product => product.is_disponible && product.is_recommande)
      .slice(0, limit);
  };

  const getProductsOnSale = () => {
    return state.products.filter(product => 
      product.is_disponible && 
      product.prix_reduit && 
      product.prix_reduit < product.prix
    );
  };

  return {
    ...state,
    refresh,
    getProductById,
    searchProducts,
    getProductsByCategory,
    getAvailableProducts,
    getProductsInPriceRange,
    getCategories,
    getPriceRange,
    getPopularProducts,
    getRecommendedProducts,
    getProductsOnSale,
    reload: loadProducts,
  };
};
