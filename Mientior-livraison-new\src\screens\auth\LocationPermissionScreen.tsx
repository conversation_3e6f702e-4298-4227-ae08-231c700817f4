import React, { useState, useRef, useEffect } from 'react';
import Constants from 'expo-constants';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  Animated,
  Dimensions,
  Platform,
  AccessibilityInfo,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../../store/authStore';
import { colors, shadows, spacing } from '../../constants/theme';

const { height: screenHeight } = Dimensions.get('window');

export const LocationPermissionScreen: React.FC = () => {
  const navigation = useNavigation();
  const { setCurrentLocation, setLocationPermission } = useAuthStore();
  const [loading, setLoading] = useState(false);
  
  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Pulse animation for location icon
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Check current permission status
    checkPermissionStatus();
  }, []);

  const checkPermissionStatus = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      console.log('Current permission status:', status);
    } catch (error) {
      console.log('Error checking permission status:', error);
    }
  };

  const handleRequestPermission = async () => {
    try {
      setLoading(true);

      // Demander la permission de géolocalisation
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('Permission request result:', status);
      
      if (status === 'granted') {
        // Permission accordée, sauvegarder le statut
        setLocationPermission(true);
        await AsyncStorage.setItem('locationPermissionGranted', 'true');

        // Obtenir la position actuelle
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          });

          console.log('Position obtenue:', location.coords);

          // Sauvegarder la position actuelle dans le store et AsyncStorage
          const currentLocation = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || undefined,
            timestamp: Date.now(),
          };

          setCurrentLocation(currentLocation);
          await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(currentLocation));

          // Announce success for accessibility
          AccessibilityInfo.announceForAccessibility('Permission de géolocalisation accordée');

          Alert.alert(
            '🎯 Permission accordée !',
            'Parfait ! Nous pourrons vous proposer les meilleurs restaurants et services près de chez vous.',
            [{
              text: 'Continuer',
              onPress: () => {
                // Success animation before navigation
                Animated.sequence([
                  Animated.timing(scaleAnim, {
                    toValue: 1.05,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                  Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                  }),
                ]).start(() => {
                  navigation.navigate('AuthChoiceScreen' as never);
                });
              }
            }]
          );
        } catch (locationError) {
          console.error('Erreur lors de l\'obtention de la position:', locationError);
          // Même si on ne peut pas obtenir la position, on continue
          // Mais on garde le statut de permission accordée
          navigation.navigate('AuthChoiceScreen' as never);
        }
      } else {
        // Permission refusée, sauvegarder le statut
        setLocationPermission(false);
        await AsyncStorage.setItem('locationPermissionGranted', 'false');

        Alert.alert(
          '📍 Permission refusée',
          'Pas de problème ! Vous pourrez toujours utiliser l\'application en saisissant votre adresse manuellement.',
          [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
        );
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      Alert.alert(
        '⚠️ Erreur',
        'Une erreur est survenue. Vous pourrez configurer la géolocalisation plus tard dans les paramètres.',
        [{ text: 'Continuer', onPress: () => navigation.navigate('AuthChoiceScreen' as never) }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    navigation.navigate('AuthChoiceScreen' as never);
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="transparent" translucent barStyle="dark-content" />

      {/* Map Background */}
      <View style={styles.mapBackground}>
        <Image
          source={{
            uri: `https://maps.googleapis.com/maps/api/staticmap?center=48.8566,2.3522&zoom=16&size=400x800&maptype=roadmap&key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s&style=feature:all%7Celement:labels%7Cvisibility:on&style=feature:all%7Celement:labels.text.fill%7Ccolor:0x616161&style=feature:all%7Celement:labels.text.stroke%7Ccolor:0xffffff&style=feature:administrative%7Celement:geometry.fill%7Ccolor:0xfefefe&style=feature:administrative%7Celement:geometry.stroke%7Ccolor:0xfefefe&style=feature:landscape%7Celement:geometry%7Ccolor:0xf5f5f5&style=feature:poi%7Celement:geometry%7Ccolor:0xf5f5f5&style=feature:poi.park%7Celement:geometry%7Ccolor:0xdedede&style=feature:road%7Celement:geometry%7Ccolor:0xffffff&style=feature:road%7Celement:geometry.stroke%7Ccolor:0xe9e9e9&style=feature:road.arterial%7Celement:geometry%7Ccolor:0xffffff&style=feature:road.highway%7Celement:geometry%7Ccolor:0xffffff&style=feature:road.highway%7Celement:geometry.stroke%7Ccolor:0xe9e9e9&style=feature:road.local%7Celement:geometry%7Ccolor:0xffffff&style=feature:transit.line%7Celement:geometry%7Ccolor:0xe5e5e5&style=feature:water%7Celement:geometry%7Ccolor:0xc9c9c9`
          }}
          style={styles.mapFullScreen}
          resizeMode="cover"
        />

        {/* Pin de localisation animé au centre */}
        <Animated.View style={[styles.centerPin, { transform: [{ scale: pulseAnim }] }]}>
          <View style={styles.pinContainer}>
            <View style={styles.pinOuter}>
              <Ionicons name="location" size={24} color="#0DCAA8" />
            </View>
            {/* Cercles de pulsation */}
            <Animated.View style={[styles.pulseCircle, { opacity: pulseAnim, transform: [{ scale: pulseAnim }] }]} />
            <Animated.View style={[styles.pulseCircle2, { opacity: pulseAnim, transform: [{ scale: pulseAnim }] }]} />
          </View>
        </Animated.View>
      </View>

      {/* Content Overlay */}
      <View style={styles.contentOverlay}>
        <Animated.View
          style={[
            styles.contentContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ],
            },
          ]}
        >
          {/* Title */}
          <Text style={styles.title}>Autoriser la localisation</Text>

          {/* Subtitle */}
          <Text style={styles.subtitle}>
            Pour une meilleure expérience de livraison, permettez-nous d'accéder à votre position
          </Text>

          {/* Benefits List */}
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="location" size={22} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Livraison précise à votre porte
              </Text>
            </View>

            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="time" size={22} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Estimation du temps plus précise
              </Text>
            </View>

            <View style={styles.benefitItem}>
              <View style={styles.benefitIconContainer}>
                <Ionicons name="navigate" size={22} color="#0DCAA8" />
              </View>
              <Text style={styles.benefitText}>
                Suivi en temps réel du livreur
              </Text>
            </View>
          </View>

          {/* Primary Button */}
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleRequestPermission}
            disabled={loading}
            accessibilityLabel="Autoriser l'accès à la localisation"
          >
            <Text style={styles.primaryButtonText}>
              {loading ? 'Chargement...' : 'Autoriser l\'accès'}
            </Text>
          </TouchableOpacity>

          {/* Secondary Button */}
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleSkip}
            disabled={loading}
            accessibilityLabel="Plus tard"
          >
            <Text style={styles.secondaryButtonText}>Plus tard</Text>
          </TouchableOpacity>

          {/* Manual Address Link */}
          <TouchableOpacity
            style={styles.manualAddressButton}
            onPress={() => navigation.navigate('ManualAddressScreen' as never)}
            accessibilityLabel="Saisir mon adresse manuellement"
          >
            <Text style={styles.manualAddressText}>Saisir mon adresse manuellement</Text>
          </TouchableOpacity>

          {/* Bottom Indicator */}
          <View style={styles.bottomIndicator}>
            <View style={styles.indicator} />
          </View>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  mapBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  mapFullScreen: {
    width: '100%',
    height: '100%',
  },
  contentOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  benefitsList: {
    width: '100%',
    marginBottom: 32,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  benefitIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(13, 202, 168, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  benefitText: {
    fontSize: 16,
    color: '#1A1A1A',
    fontWeight: '500',
  },
  primaryButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 16,
    paddingVertical: 16,
    width: '100%',
    marginBottom: 16,
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  secondaryButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingVertical: 16,
    width: '100%',
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    textAlign: 'center',
  },
  manualAddressButton: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  manualAddressText: {
    fontSize: 14,
    color: '#6B7280',
    textDecorationLine: 'underline',
  },
  centerPin: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -20,
    marginTop: -40,
  },
  pinContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pinOuter: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  pulseCircle: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(13, 202, 168, 0.3)',
    top: -10,
    left: -10,
  },
  pulseCircle2: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(13, 202, 168, 0.15)',
    top: -20,
    left: -20,
  },
  bottomIndicator: {
    width: '100%',
    alignItems: 'center',
    marginTop: 16,
  },
  indicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#E5E7EB',
  },
});

export default LocationPermissionScreen;
