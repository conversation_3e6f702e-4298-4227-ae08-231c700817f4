import { useState, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { useAuth } from './useAuth';
import { Notification } from '../types';
import { notificationService } from '../services/supabase';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  expoPushToken: string | null;
  hasPermission: boolean;
  refreshNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  sendLocalNotification: (title: string, body: string, data?: any, channelId?: string) => Promise<void>;
}

// Configuration des notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export const useNotifications = () => {
  const { user } = useAuth();
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    expoPushToken: null,
    hasPermission: false,
    refreshNotifications: async () => {},
    markAsRead: async () => {},
    markAllAsRead: async () => {},
    sendLocalNotification: async () => {},
  });

  const notificationListener = useRef<any>(null);
  const responseListener = useRef<any>(null);

  const initializeNotifications = async () => {
    try {
      // Demander les permissions pour les notifications locales
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Permission de notification refusée');
        setState(prev => ({ ...prev, hasPermission: false }));
        return;
      }

      setState(prev => ({ ...prev, hasPermission: true }));

      // Configuration pour Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Notifications Mientior',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
        });

        // Canal pour les commandes
        await Notifications.setNotificationChannelAsync('orders', {
          name: 'Commandes',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          sound: 'default',
        });

        // Canal pour les livraisons
        await Notifications.setNotificationChannelAsync('delivery', {
          name: 'Livraisons',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 500, 250, 500],
          sound: 'default',
        });
      }

      // Écouter les notifications reçues
      notificationListener.current = Notifications.addNotificationReceivedListener(
        handleNotificationReceived
      );

      // Écouter les interactions avec les notifications
      responseListener.current = Notifications.addNotificationResponseReceivedListener(
        handleNotificationResponse
      );

      // Note: Les push notifications ne sont pas disponibles dans Expo Go
      // Elles nécessitent un development build
      console.log('Notifications locales initialisées avec succès');

    } catch (error) {
      console.error('Erreur lors de l\'initialisation des notifications:', error);
      setState(prev => ({ 
        ...prev, 
        error: 'Erreur lors de l\'initialisation des notifications' 
      }));
    }
  };

  const handleNotificationReceived = (notification: Notifications.Notification) => {
    const notificationData: Notification = {
      id: notification.request.identifier,
      titre: notification.request.content.title || '',
      contenu: notification.request.content.body || '',
      data_json: notification.request.content.data,
      created_at: new Date().toISOString(),
      is_read: false,
      is_lu: false,
      type: (notification.request.content.data?.type as Notification['type']) || 'systeme',
      user_id: user?.id || '', // Assuming user is available here
    };

    setState(prev => ({
      ...prev,
      notifications: [notificationData, ...prev.notifications],
      unreadCount: prev.unreadCount + 1,
    }));
  };

  const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const notification = response.notification;
    const data = notification.request.content.data;

    // Gérer les actions basées sur le type de notification
    if (data?.type === 'order' && data?.orderId) {
      // Naviguer vers l'écran de commande
      console.log('Navigation vers commande:', data.orderId);
    } else if (data?.type === 'delivery' && data?.deliveryId) {
      // Naviguer vers le tracking
      console.log('Navigation vers tracking:', data.deliveryId);
    }

    // Marquer comme lu
    // Mark as read - this will be handled by the markAsRead function returned by the hook
    // We don't call it directly here to avoid circular dependencies or unexpected state updates
    // when the notification is first received.
  };

  const loadNotifications = async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      // Fetch notifications from Supabase
      const data = await notificationService.getByUser(user.id);

      const unread = data?.filter((n: Notification) => !n.is_read).length || 0;

      setState(prev => ({
        ...prev,
        notifications: data || [],
        unreadCount: unread,
        loading: false,
      }));
    } catch (err: any) {
      console.error('Error loading notifications:', err.message);
      setState(prev => ({
        ...prev,
        error: 'Failed to load notifications: ' + err.message,
        loading: false,
      }));
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n =>
          n.id === notificationId ? { ...n, is_read: true, is_lu: true } : n
        ),
        unreadCount: prev.unreadCount - (prev.notifications.find(n => n.id === notificationId && !n.is_read) ? 1 : 0),
      }));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user) return;
    try {
      await notificationService.markAllAsRead(user.id);
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, is_read: true, is_lu: true })),
        unreadCount: 0,
      }));
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const sendLocalNotification = async (title: string, body: string, data?: any) => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: null, // now
    });
  };

  const scheduledNotification = async (
    title: string,
    body: string,
    triggerDate: Date,
    data?: any
  ) => {
    try {
      // Calculate seconds from now to the trigger date
      const now = new Date();
      const secondsFromNow = Math.max(1, Math.floor((triggerDate.getTime() - now.getTime()) / 1000));

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: {
          seconds: secondsFromNow,
        } as Notifications.TimeIntervalTriggerInput,
        identifier: `scheduled_${Date.now()}`,
      });
    } catch (error) {
      console.error('Erreur lors de la programmation de notification:', error);
    }
  };

  const cancelNotification = async (identifier: string) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Erreur lors de l\'annulation de notification:', error);
    }
  };

  const setBadgeCount = async (count: number) => {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du badge:', error);
    }
  };

  const getNotificationsByType = (type: Notification['type']) => {
    return state.notifications.filter(n => n.type === type);
  };

  const getUnreadNotifications = () => {
    return state.notifications.filter(n => !n.is_read);
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.filter(n => n.id !== notificationId),
        unreadCount: prev.notifications.find(n => n.id === notificationId && !n.is_read) 
          ? prev.unreadCount - 1 
          : prev.unreadCount,
      }));
    } catch (error) {
      console.error('Erreur lors de la suppression de notification:', error);
    }
  };

  const refreshNotifications = async () => {
    if (user) {
      await loadNotifications();
    }
  };

  const clearAllNotifications = () => {
    setState(prev => ({
      ...prev,
      notifications: [],
      unreadCount: 0,
    }));
  };

  useEffect(() => {
    if (user) {
      initializeNotifications();
      loadNotifications();
    }

    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (responseListener.current) {
        responseListener.current.remove();
      }
    };
  }, [user]);

  return {
    ...state,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    sendLocalNotification,
    scheduledNotification,
    cancelNotification,
    setBadgeCount,
    getNotificationsByType,
    getUnreadNotifications,
    deleteNotification,
    clearAllNotifications,
    refreshNotifications,
    registerForPushNotifications: async () => {
      console.log('🔔 Initialisation des notifications...');
      return state.expoPushToken;
    },
  };
};
