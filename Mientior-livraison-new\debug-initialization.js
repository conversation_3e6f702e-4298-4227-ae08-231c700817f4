/**
 * Script de diagnostic pour tester les corrections d'initialisation
 * Exécuter avec: node debug-initialization.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC D\'INITIALISATION - Livraison Afrique');
console.log('='.repeat(60));

// 1. Vérifier les fichiers critiques
const criticalFiles = [
  'src/navigation/AppNavigator.tsx',
  'src/store/authStore.ts',
  'src/utils/debugInitialization.ts',
  'src/screens/LoadingScreen.tsx'
];

console.log('\n📁 Vérification des fichiers critiques:');
criticalFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
});

// 2. Vérifier les timeouts dans AppNavigator
console.log('\n⏰ Vérification des timeouts dans AppNavigator:');
try {
  const appNavContent = fs.readFileSync('src/navigation/AppNavigator.tsx', 'utf8');
  
  // Vérifier timeout de sécurité (8 secondes)
  const securityTimeout = appNavContent.includes('8000');
  console.log(`  ${securityTimeout ? '✅' : '❌'} Timeout de sécurité réduit à 8s`);
  
  // Vérifier timeout auth (6 secondes)
  const authTimeout = appNavContent.includes('6000');
  console.log(`  ${authTimeout ? '✅' : '❌'} Timeout auth réduit à 6s`);
  
  // Vérifier utilisation du debugger
  const hasDebugger = appNavContent.includes('InitializationDebugger');
  console.log(`  ${hasDebugger ? '✅' : '❌'} Système de débogage intégré`);
  
} catch (error) {
  console.log(`  ❌ Erreur lecture AppNavigator: ${error.message}`);
}

// 3. Vérifier les timeouts dans authStore
console.log('\n🔐 Vérification des timeouts dans authStore:');
try {
  const authStoreContent = fs.readFileSync('src/store/authStore.ts', 'utf8');
  
  // Vérifier timeout session (3 secondes)
  const sessionTimeout = authStoreContent.includes('3000');
  console.log(`  ${sessionTimeout ? '✅' : '❌'} Timeout session réduit à 3s`);
  
  // Vérifier gestion d'erreur améliorée
  const errorHandling = authStoreContent.includes('initialized: true');
  console.log(`  ${errorHandling ? '✅' : '❌'} Gestion d'erreur améliorée`);
  
} catch (error) {
  console.log(`  ❌ Erreur lecture authStore: ${error.message}`);
}

// 4. Vérifier la configuration Supabase
console.log('\n🗄️ Vérification de la configuration Supabase:');
try {
  const envExists = fs.existsSync('.env');
  console.log(`  ${envExists ? '✅' : '❌'} Fichier .env présent`);
  
  if (envExists) {
    const envContent = fs.readFileSync('.env', 'utf8');
    const hasSupabaseUrl = envContent.includes('EXPO_PUBLIC_SUPABASE_URL');
    const hasSupabaseKey = envContent.includes('EXPO_PUBLIC_SUPABASE_ANON_KEY');
    
    console.log(`  ${hasSupabaseUrl ? '✅' : '❌'} EXPO_PUBLIC_SUPABASE_URL configuré`);
    console.log(`  ${hasSupabaseKey ? '✅' : '❌'} EXPO_PUBLIC_SUPABASE_ANON_KEY configuré`);
  }
} catch (error) {
  console.log(`  ❌ Erreur lecture .env: ${error.message}`);
}

// 5. Recommandations
console.log('\n💡 RECOMMANDATIONS:');
console.log('  1. Tester l\'app sur un appareil physique ou émulateur');
console.log('  2. Vérifier les logs de la console pour les messages de débogage');
console.log('  3. Si le problème persiste, vérifier la connectivité Supabase');
console.log('  4. Le timeout de sécurité devrait déclencher après 8 secondes maximum');

// 6. Commandes utiles
console.log('\n🛠️ COMMANDES UTILES:');
console.log('  npx expo start --clear    # Redémarrer avec cache vidé');
console.log('  npx expo run:android      # Lancer sur Android');
console.log('  npx expo run:ios          # Lancer sur iOS');
console.log('  npx expo doctor           # Diagnostiquer les problèmes');

console.log('\n' + '='.repeat(60));
console.log('✅ Diagnostic terminé. Les corrections ont été appliquées.');
console.log('🚀 L\'app devrait maintenant se débloquer après maximum 8 secondes.');
