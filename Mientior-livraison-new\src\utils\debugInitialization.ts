/**
 * Utilitaires de débogage pour l'initialisation de l'application
 * Aide à diagnostiquer les problèmes de blocage à l'initialisation
 */

export class InitializationDebugger {
  private static logs: string[] = [];
  private static startTime = Date.now();

  static log(message: string, data?: any) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ${message}`;
    
    console.log(logEntry, data || '');
    this.logs.push(logEntry + (data ? ` ${JSON.stringify(data)}` : ''));
    
    // Garder seulement les 50 derniers logs
    if (this.logs.length > 50) {
      this.logs = this.logs.slice(-50);
    }
  }

  static error(message: string, error?: any) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ❌ ERROR: ${message}`;
    
    console.error(logEntry, error || '');
    this.logs.push(logEntry + (error ? ` ${error.toString()}` : ''));
  }

  static warning(message: string, data?: any) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ⚠️ WARNING: ${message}`;
    
    console.warn(logEntry, data || '');
    this.logs.push(logEntry + (data ? ` ${JSON.stringify(data)}` : ''));
  }

  static success(message: string, data?: any) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ✅ SUCCESS: ${message}`;
    
    console.log(logEntry, data || '');
    this.logs.push(logEntry + (data ? ` ${JSON.stringify(data)}` : ''));
  }

  static timeout(operation: string, timeoutMs: number) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ⏰ TIMEOUT: ${operation} après ${timeoutMs}ms`;
    
    console.warn(logEntry);
    this.logs.push(logEntry);
  }

  static getAllLogs(): string[] {
    return [...this.logs];
  }

  static getLogsAsString(): string {
    return this.logs.join('\n');
  }

  static clear() {
    this.logs = [];
    this.startTime = Date.now();
  }

  static exportLogs(): string {
    const header = `=== INITIALIZATION DEBUG LOGS ===\nStart Time: ${new Date(this.startTime).toISOString()}\nCurrent Time: ${new Date().toISOString()}\nTotal Duration: ${Date.now() - this.startTime}ms\n\n`;
    return header + this.getLogsAsString();
  }
}

/**
 * Wrapper pour les opérations avec timeout et logging automatique
 */
export async function withTimeoutAndLogging<T>(
  operation: () => Promise<T>,
  operationName: string,
  timeoutMs: number = 5000
): Promise<T> {
  InitializationDebugger.log(`🚀 Début: ${operationName}`);
  
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      InitializationDebugger.timeout(operationName, timeoutMs);
      reject(new Error(`Timeout: ${operationName} après ${timeoutMs}ms`));
    }, timeoutMs);
  });

  try {
    const result = await Promise.race([operation(), timeoutPromise]);
    InitializationDebugger.success(`${operationName} terminé`);
    return result;
  } catch (error) {
    InitializationDebugger.error(`${operationName} échoué`, error);
    throw error;
  }
}

/**
 * Moniteur de performance pour l'initialisation
 */
export class PerformanceMonitor {
  private static checkpoints: { [key: string]: number } = {};

  static checkpoint(name: string) {
    const timestamp = Date.now();
    this.checkpoints[name] = timestamp;
    InitializationDebugger.log(`📍 Checkpoint: ${name}`);
  }

  static getTimeBetween(start: string, end: string): number | null {
    if (!this.checkpoints[start] || !this.checkpoints[end]) {
      return null;
    }
    return this.checkpoints[end] - this.checkpoints[start];
  }

  static getReport(): string {
    const report = ['=== PERFORMANCE REPORT ==='];
    const checkpointNames = Object.keys(this.checkpoints).sort(
      (a, b) => this.checkpoints[a] - this.checkpoints[b]
    );

    for (let i = 0; i < checkpointNames.length; i++) {
      const name = checkpointNames[i];
      const timestamp = this.checkpoints[name];
      const relativeTime = timestamp - InitializationDebugger['startTime'];
      
      report.push(`${name}: ${relativeTime}ms`);
      
      if (i > 0) {
        const prevName = checkpointNames[i - 1];
        const duration = timestamp - this.checkpoints[prevName];
        report.push(`  └─ Durée depuis ${prevName}: ${duration}ms`);
      }
    }

    return report.join('\n');
  }

  static clear() {
    this.checkpoints = {};
  }
}
