import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { colors } from '../constants/theme';

interface ErrorBoundaryProps {
  error: string;
  onRetry: () => void;
  onHelp?: () => void;
  title?: string;
  icon?: string;
  retryText?: string;
  helpText?: string;
  showHelp?: boolean;
}

const { width } = Dimensions.get('window');

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  error,
  onRetry,
  onHelp,
  title = "Oups ! Un problème est survenu",
  icon = "📡",
  retryText = "🔄 Réessayer",
  helpText = "💬 Besoin d'aide ?",
  showHelp = true,
}) => {
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  const getErrorType = (errorMessage: string) => {
    if (errorMessage.includes('connexion') || errorMessage.includes('réseau') || errorMessage.includes('fetch')) {
      return { icon: '📡', color: colors.warning };
    }
    if (errorMessage.includes('timeout') || errorMessage.includes('temps')) {
      return { icon: '⏱️', color: colors.warning };
    }
    if (errorMessage.includes('indisponible') || errorMessage.includes('service')) {
      return { icon: '🔧', color: colors.info };
    }
    if (errorMessage.includes('relationship') || errorMessage.includes('schema') || errorMessage.includes('PGRST')) {
      return { icon: '🗄️', color: colors.info };
    }
    if (errorMessage.includes('toLowerCase') || errorMessage.includes('undefined')) {
      return { icon: '⚠️', color: colors.warning };
    }
    return { icon: '❌', color: colors.error };
  };

  const errorType = getErrorType(error);

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={[styles.iconContainer, { backgroundColor: errorType.color + '20' }]}>
        <Text style={styles.iconText}>{errorType.icon}</Text>
      </View>
      
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.errorText}>{error}</Text>
      
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>{retryText}</Text>
      </TouchableOpacity>
      
      {showHelp && (
        <TouchableOpacity 
          style={styles.helpButton} 
          onPress={onHelp || (() => console.log('Aide demandée'))}
        >
          <Text style={styles.helpButtonText}>{helpText}</Text>
        </TouchableOpacity>
      )}
      
      <View style={styles.tipsContainer}>
        <Text style={styles.tipsTitle}>💡 Conseils :</Text>
        <Text style={styles.tipsText}>
          • Vérifiez votre connexion internet{'\n'}
          • Réessayez dans quelques instants{'\n'}
          • Contactez le support si le problème persiste
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.background.primary,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconText: {
    fontSize: 40,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 20,
    maxWidth: width - 80,
  },
  retryButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.primary[500],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    minWidth: 200,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  helpButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border.light,
    marginBottom: 24,
  },
  helpButtonText: {
    color: colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  tipsContainer: {
    backgroundColor: colors.background.secondary,
    padding: 16,
    borderRadius: 12,
    maxWidth: width - 40,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 13,
    color: colors.text.secondary,
    lineHeight: 18,
  },
});

export default ErrorBoundary;
