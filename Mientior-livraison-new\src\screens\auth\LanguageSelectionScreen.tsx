import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Animatable from 'react-native-animatable';

interface Language {
  code: string;
  name: string;
  flag: string;
}

  const languages: Language[] = [
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
    { code: 'sw', name: 'Kiswahili', flag: '🇰🇪' },
    { code: 'ha', name: 'Hausa', flag: '🇳🇬' },
    { code: 'yo', name: 'Yorùbá', flag: '🇳🇬' },
    { code: 'ig', name: 'Ig<PERSON>', flag: '🇳🇬' },
    { code: 'am', name: 'አማርኛ', flag: '🇪🇹' },
  ];

const LanguageSelectionScreen = () => {
  const navigation = useNavigation();
  const [selectedLanguage, setSelectedLanguage] = useState('fr');

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    console.log(`Langue sélectionnée: ${languageCode}`);
  };

  const handleContinue = async () => {
    try {
      // Sauvegarder la langue sélectionnée
      await AsyncStorage.setItem('selectedLanguage', selectedLanguage);
      console.log(`Langue sauvegardée: ${selectedLanguage}`);

      navigation.navigate('LocationPermission' as never);
    } catch (error) {
      console.error('Erreur sauvegarde langue:', error);
      // Continuer même en cas d'erreur
      navigation.navigate('LocationPermission' as never);
    }
  };

  const renderLanguageItem = ({ item }: { item: Language }) => {
    const isSelected = selectedLanguage === item.code;
    
    return (
      <Animatable.View 
        animation="fadeInUp"
        duration={800}
        style={styles.languageItemContainer}
      >
        <TouchableOpacity
          style={[
            styles.languageItem,
            isSelected && styles.selectedLanguageItem
          ]}
          onPress={() => handleLanguageSelect(item.code)}
          activeOpacity={0.7}
        >
          <View style={styles.flagNameContainer}>
            <Text style={styles.flag}>{item.flag}</Text>
            <Text style={styles.languageName}>{item.name}</Text>
          </View>
          
          {isSelected && (
            <View style={styles.checkmarkContainer}>
              <Ionicons name="checkmark-circle" size={20} color="#0DCAA8" />
            </View>
          )}
        </TouchableOpacity>
      </Animatable.View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#F7FCFA" barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color="#0D1C17" />
        </TouchableOpacity>
        <Text style={styles.title}>Language</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.subtitle}>Choose your language</Text>
        
        <FlatList
          key="single-column"
          data={languages}
          renderItem={renderLanguageItem}
          keyExtractor={(item) => item.code}
          contentContainerStyle={styles.listContainer}
        />
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
          activeOpacity={0.8}
        >
          <Text style={styles.continueButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FCFA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#F7FCFA',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0D1C17',
    fontFamily: 'PlusJakartaSans-Bold',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  subtitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#0D1C17',
    marginBottom: 20,
    paddingHorizontal: 16,
    fontFamily: 'PlusJakartaSans-Bold',
  },
  listContainer: {
    paddingBottom: 150,
  },
  languageItemContainer: {
    marginBottom: 12,
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#CCE8DE',
    borderRadius: 8,
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  selectedLanguageItem: {
    borderColor: '#0DCAA8',
    borderWidth: 2,
  },
  flagNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  flag: {
    fontSize: 20,
    marginRight: 12,
  },
  languageName: {
    fontSize: 14,
    color: '#0D1C17',
    fontFamily: 'PlusJakartaSans-Medium',
  },
  checkmarkContainer: {
    marginLeft: 8,
  },
  footer: {
    padding: 16,
    backgroundColor: '#F7FCFA',
    borderTopWidth: 1,
    borderTopColor: '#CCE8DE',
  },
  continueButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'PlusJakartaSans-Bold',
  },
});

export default LanguageSelectionScreen;
