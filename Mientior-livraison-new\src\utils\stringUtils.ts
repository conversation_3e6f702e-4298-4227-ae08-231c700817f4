/**
 * Utilitaires pour la manipulation sécurisée des chaînes de caractères
 * Évite les erreurs "Cannot read property 'toLowerCase' of undefined"
 */

/**
 * Convertit une chaîne en minuscules de manière sécurisée
 * @param str - La chaîne à convertir
 * @param fallback - Valeur de retour si str est null/undefined
 * @returns La chaîne en minuscules ou la valeur de fallback
 */
export const safeToLowerCase = (str: any, fallback: string = ''): string => {
  if (str === null || str === undefined) {
    return fallback;
  }
  
  if (typeof str !== 'string') {
    return String(str).toLowerCase();
  }
  
  return str.toLowerCase();
};

/**
 * Convertit une chaîne en majuscules de manière sécurisée
 * @param str - La chaîne à convertir
 * @param fallback - Valeur de retour si str est null/undefined
 * @returns La chaîne en majuscules ou la valeur de fallback
 */
export const safeToUpperCase = (str: any, fallback: string = ''): string => {
  if (str === null || str === undefined) {
    return fallback;
  }
  
  if (typeof str !== 'string') {
    return String(str).toUpperCase();
  }
  
  return str.toUpperCase();
};

/**
 * Supprime les espaces en début et fin de chaîne de manière sécurisée
 * @param str - La chaîne à nettoyer
 * @param fallback - Valeur de retour si str est null/undefined
 * @returns La chaîne nettoyée ou la valeur de fallback
 */
export const safeTrim = (str: any, fallback: string = ''): string => {
  if (str === null || str === undefined) {
    return fallback;
  }
  
  if (typeof str !== 'string') {
    return String(str).trim();
  }
  
  return str.trim();
};

/**
 * Obtient le nom du jour de la semaine de manière sécurisée
 * @param date - La date
 * @param locale - La locale (par défaut 'fr-FR')
 * @param fallback - Valeur de retour en cas d'erreur
 * @returns Le nom du jour en minuscules ou la valeur de fallback
 */
export const getSafeDayName = (date: Date = new Date(), locale: string = 'fr-FR', fallback: string = 'lundi'): string => {
  try {
    const dayName = date.toLocaleString(locale, { weekday: 'long' });
    return safeToLowerCase(dayName, fallback);
  } catch (error) {
    console.warn('Erreur lors de la récupération du nom du jour:', error);
    return fallback;
  }
};

/**
 * Valide et nettoie un email de manière sécurisée
 * @param email - L'email à valider
 * @returns L'email nettoyé et en minuscules, ou une chaîne vide si invalide
 */
export const sanitizeEmail = (email: any): string => {
  if (!email || typeof email !== 'string') {
    return '';
  }
  
  const trimmed = safeTrim(email);
  if (!trimmed) {
    return '';
  }
  
  return safeToLowerCase(trimmed);
};

/**
 * Vérifie si une chaîne est valide (non null, non undefined, non vide après trim)
 * @param str - La chaîne à vérifier
 * @returns true si la chaîne est valide
 */
export const isValidString = (str: any): str is string => {
  return str !== null && str !== undefined && typeof str === 'string' && safeTrim(str).length > 0;
};

/**
 * Convertit une valeur en chaîne de manière sécurisée
 * @param value - La valeur à convertir
 * @param fallback - Valeur de retour si la conversion échoue
 * @returns La chaîne convertie ou la valeur de fallback
 */
export const safeToString = (value: any, fallback: string = ''): string => {
  if (value === null || value === undefined) {
    return fallback;
  }
  
  try {
    return String(value);
  } catch (error) {
    console.warn('Erreur lors de la conversion en chaîne:', error);
    return fallback;
  }
};

/**
 * Formate un nom de manière sécurisée (première lettre en majuscule)
 * @param name - Le nom à formater
 * @param fallback - Valeur de retour si le nom est invalide
 * @returns Le nom formaté ou la valeur de fallback
 */
export const formatName = (name: any, fallback: string = ''): string => {
  const safeName = safeTrim(name, fallback);
  if (!safeName) {
    return fallback;
  }
  
  return safeName.charAt(0).toUpperCase() + safeToLowerCase(safeName.slice(1));
};

/**
 * Nettoie et formate un numéro de téléphone
 * @param phone - Le numéro de téléphone
 * @returns Le numéro nettoyé (chiffres et + seulement)
 */
export const sanitizePhone = (phone: any): string => {
  const safePhone = safeToString(phone);
  if (!safePhone) {
    return '';
  }
  
  // Garder seulement les chiffres et le signe +
  return safePhone.replace(/[^\d+]/g, '');
};

/**
 * Vérifie si deux chaînes sont égales en ignorant la casse et les espaces
 * @param str1 - Première chaîne
 * @param str2 - Deuxième chaîne
 * @returns true si les chaînes sont équivalentes
 */
export const isEqualIgnoreCase = (str1: any, str2: any): boolean => {
  const safe1 = safeToLowerCase(safeTrim(str1));
  const safe2 = safeToLowerCase(safeTrim(str2));
  return safe1 === safe2;
};

/**
 * Tronque une chaîne à une longueur maximale
 * @param str - La chaîne à tronquer
 * @param maxLength - Longueur maximale
 * @param suffix - Suffixe à ajouter si tronqué (par défaut '...')
 * @returns La chaîne tronquée
 */
export const truncateString = (str: any, maxLength: number, suffix: string = '...'): string => {
  const safeStr = safeToString(str);
  if (safeStr.length <= maxLength) {
    return safeStr;
  }
  
  return safeStr.substring(0, maxLength - suffix.length) + suffix;
};
