import React, { Component, ReactNode } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { colors } from '../constants/theme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

const { width } = Dimensions.get('window');

class ReactErrorBoundary extends Component<Props, State> {
  private fadeAnim = new Animated.Value(0);
  private scaleAnim = new Animated.Value(0.8);

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('🚨 Error Boundary caught an error:', error);
    console.error('🚨 Error Info:', errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Animer l'apparition de l'écran d'erreur
    Animated.parallel([
      Animated.timing(this.fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(this.scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }

  getErrorType = (errorMessage: string) => {
    if (errorMessage.includes('toLowerCase') || errorMessage.includes('undefined')) {
      return { icon: '⚠️', color: colors.warning, title: 'Erreur de données' };
    }
    if (errorMessage.includes('connexion') || errorMessage.includes('réseau') || errorMessage.includes('fetch')) {
      return { icon: '📡', color: colors.warning, title: 'Problème de connexion' };
    }
    if (errorMessage.includes('timeout') || errorMessage.includes('temps')) {
      return { icon: '⏱️', color: colors.warning, title: 'Délai d\'attente dépassé' };
    }
    if (errorMessage.includes('relationship') || errorMessage.includes('schema') || errorMessage.includes('PGRST')) {
      return { icon: '🗄️', color: colors.info, title: 'Problème de base de données' };
    }
    return { icon: '❌', color: colors.error, title: 'Erreur inattendue' };
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    
    // Réinitialiser les animations
    this.fadeAnim.setValue(0);
    this.scaleAnim.setValue(0.8);
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorMessage = this.state.error?.message || 'Une erreur inattendue s\'est produite';
      const errorType = this.getErrorType(errorMessage);

      return (
        <Animated.View 
          style={[
            styles.container,
            {
              opacity: this.fadeAnim,
              transform: [{ scale: this.scaleAnim }],
            },
          ]}
        >
          <View style={[styles.iconContainer, { backgroundColor: errorType.color + '20' }]}>
            <Text style={styles.iconText}>{errorType.icon}</Text>
          </View>
          
          <Text style={styles.title}>{errorType.title}</Text>
          <Text style={styles.errorText}>
            {errorMessage.length > 100 ? errorMessage.substring(0, 100) + '...' : errorMessage}
          </Text>
          
          <TouchableOpacity style={styles.retryButton} onPress={this.handleRetry}>
            <Text style={styles.retryButtonText}>🔄 Réessayer</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.helpButton} 
            onPress={() => console.log('Aide demandée pour:', errorMessage)}
          >
            <Text style={styles.helpButtonText}>💬 Besoin d'aide ?</Text>
          </TouchableOpacity>
          
          <View style={styles.tipsContainer}>
            <Text style={styles.tipsTitle}>💡 Conseils :</Text>
            <Text style={styles.tipsText}>
              • Vérifiez votre connexion internet{'\n'}
              • Réessayez dans quelques instants{'\n'}
              • Redémarrez l'application si nécessaire
            </Text>
          </View>

          {__DEV__ && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>🔧 Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error?.stack?.substring(0, 200)}...
              </Text>
            </View>
          )}
        </Animated.View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.background.primary,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconText: {
    fontSize: 40,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 20,
    maxWidth: width - 80,
  },
  retryButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.primary[500],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    minWidth: 200,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  helpButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border.light,
    marginBottom: 24,
  },
  helpButtonText: {
    color: colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  tipsContainer: {
    backgroundColor: colors.background.secondary,
    padding: 16,
    borderRadius: 12,
    maxWidth: width - 40,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
    marginBottom: 16,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 13,
    color: colors.text.secondary,
    lineHeight: 18,
  },
  debugContainer: {
    backgroundColor: colors.background.tertiary,
    padding: 12,
    borderRadius: 8,
    maxWidth: width - 40,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  debugText: {
    fontSize: 10,
    color: colors.text.secondary,
    fontFamily: 'monospace',
  },
});

export default ReactErrorBoundary;
