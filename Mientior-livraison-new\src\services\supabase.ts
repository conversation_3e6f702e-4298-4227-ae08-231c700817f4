import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  User,
  merchant,
  Produit,
  Commande,
  Livraison,
  Paiement,
  Address,
  Notification,
  Evaluation,
  StatistiquesLivreur,
  AnalyticsMarchand,
  FiltresRecherche,
  ResultatRecherche,
  Location
} from '../types';

// Configuration Supabase
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Variables d\'environnement Supabase manquantes');
}

// Création du client Supabase avec stockage local et WebSockets désactivés
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    // Désactiver complètement Realtime pour éviter les problèmes WebSocket
    heartbeatIntervalMs: 0,
    reconnectAfterMs: () => false,
    encode: (payload: any, callback: (encoded: string) => void) => callback(''),
    decode: (payload: any, callback: (decoded: any) => void) => callback({}),
  },
  global: {
    headers: {
      'x-my-custom-header': 'my-app-name',
    },
  },
});

// Services d'authentification
export const authService = {
  // Inscription utilisateur
  async signUp(email: string, password: string, userData: Partial<User>) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            phone: userData.phone,
            role: userData.role || 'client',
          }
        }
      });

      if (error) throw error;

      // Créer le profil utilisateur dans la table users
      if (data.user) {
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email,
            phone: userData.phone,
            role: userData.role || 'client',
            full_name: userData.full_name,
            is_active: true,
            status: 'en_verification',
            preferred_language: 'fr',
          });

        if (profileError) {
          console.error('Erreur création profil:', profileError);
          throw profileError;
        }

        // Créer le profil spécifique selon le rôle
        if (userData.role === 'client' || !userData.role) {
          const { error: clientError } = await supabase
            .from('client_profiles')
            .insert({
              user_id: data.user.id,
              first_name: (userData as any).first_name || (userData as any).full_name?.split(' ')[0] || 'Prénom',
              last_name: (userData as any).last_name || (userData as any).full_name?.split(' ').slice(1).join(' ') || 'Nom',
            });

          if (clientError) {
            console.error('Erreur création profil client:', clientError);
          }
        }
      }

      return data;
    } catch (error) {
      console.error('Erreur d\'inscription:', error);
      throw error;
    }
  },

  // Connexion utilisateur
  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Erreur de connexion:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Erreur de connexion:', error);
      throw error;
    }
  },

  // Déconnexion
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Récupérer l'utilisateur actuel
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 Récupération utilisateur Supabase...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        console.error('❌ Erreur auth getUser:', authError);
        return null;
      }

      if (!user) {
        console.log('ℹ️ Pas d\'utilisateur authentifié');
        return null;
      }

      console.log('👤 Utilisateur auth trouvé:', user.id);

      // Essayer de récupérer le profil depuis la table users
      const { data: profile, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();

      if (error) {
        console.error('❌ Erreur récupération profil depuis table users:', error);

        // Si la table n'existe pas ou autre erreur, créer un profil temporaire
        console.log('🔧 Création profil temporaire...');
        const tempProfile: User = {
          id: user.id,
          email: user.email || '',
          phone: user.phone || '',
          full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Utilisateur',
          role: 'client',
          avatar_url: user.user_metadata?.avatar_url,
          is_active: true,
          created_at: user.created_at,
          updated_at: new Date().toISOString(),
        };

        console.log('✅ Profil temporaire créé:', tempProfile.full_name);
        return tempProfile;
      }

      if (!profile) {
        console.log('ℹ️ Pas de profil trouvé, création profil temporaire...');
        // Créer un profil temporaire si aucun profil n'existe
        const tempProfile: User = {
          id: user.id,
          email: user.email || '',
          phone: user.phone || '',
          full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Utilisateur',
          role: 'client',
          avatar_url: user.user_metadata?.avatar_url,
          is_active: true,
          created_at: user.created_at,
          updated_at: new Date().toISOString(),
        };

        console.log('✅ Profil temporaire créé:', tempProfile.full_name);
        return tempProfile;
      }

      console.log('✅ Profil utilisateur récupéré:', profile.full_name);
      return profile;
    } catch (error) {
      console.error('❌ Erreur getCurrentUser:', error);
      return null;
    }
  },

  // Mettre à jour le profil
  async updateProfile(userId: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Réinitialiser le mot de passe
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://livraison-afrique.com/reset-password',
    });
    if (error) throw error;
  },

  // Mettre à jour le mot de passe avec token
  async updatePasswordWithToken(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });
    if (error) throw error;
  },

  // Mettre à jour le mot de passe (utilisateur connecté)
  async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });
    if (error) throw error;
  },

  // Authentification par téléphone
  async signUpWithPhone(phone: string, password: string, userData: Partial<User>) {
    try {
      const { data, error } = await supabase.auth.signUp({
        phone,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            role: userData.role || 'client',
          }
        }
      });

      if (error) throw error;

      // Créer le profil utilisateur
      if (data.user) {
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            phone: data.user.phone,
            role: userData.role || 'client',
            full_name: userData.full_name,
            is_active: true,
            status: 'en_verification',
            preferred_language: 'fr',
          });

        if (profileError) {
          console.error('Erreur création profil:', profileError);
          throw profileError;
        }
      }

      return data;
    } catch (error) {
      console.error('Erreur inscription téléphone:', error);
      throw error;
    }
  },

  // Connexion par téléphone
  async signInWithPhone(phone: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        phone,
        password,
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur connexion téléphone:', error);
      throw error;
    }
  },

  // Vérification OTP améliorée
  async verifyOTP(emailOrPhone: string, token: string, type: 'signup' | 'recovery' | 'sms') {
    try {
      const isPhone = /^\+/.test(emailOrPhone);

      const { data, error } = await supabase.auth.verifyOtp({
        [isPhone ? 'phone' : 'email']: emailOrPhone,
        token,
        type: type === 'sms' ? 'sms' : (type === 'signup' ? 'signup' : 'recovery'),
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Erreur vérification OTP:', error);
      throw error;
    }
  },

  // Renvoyer OTP amélioré
  async resendOTP(emailOrPhone: string, type: 'signup' | 'recovery' | 'sms') {
    try {
      const isPhone = /^\+/.test(emailOrPhone);

      if (type === 'sms' && isPhone) {
        const { error } = await supabase.auth.resend({
          type: 'sms',
          phone: emailOrPhone,
        });
        if (error) throw error;
      } else if (type === 'signup') {
        const { error } = await supabase.auth.resend({
          type: 'signup',
          email: emailOrPhone,
        });
        if (error) throw error;
      } else {
        const { error } = await supabase.auth.resetPasswordForEmail(emailOrPhone);
        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Erreur renvoi OTP:', error);
      throw error;
    }
  },

  // Connexion sociale (préparation)
  async signInWithProvider(provider: 'google' | 'facebook' | 'apple') {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: 'https://livraison-afrique.com/auth/callback',
        },
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error(`Erreur lors de la connexion ${provider}:`, error);
      throw error;
    }
  },

  // Récupérer les informations "Se souvenir de moi"
  async getRememberedCredentials() {
    try {
      const rememberedEmail = await AsyncStorage.getItem('rememberedEmail');
      const rememberMe = await AsyncStorage.getItem('rememberMe');

      return {
        email: rememberedEmail || '',
        rememberMe: rememberMe === 'true',
      };
    } catch (error) {
      console.error('Erreur récupération credentials:', error);
      return { email: '', rememberMe: false };
    }
  },
};

// Services pour les établissements
export const merchant_profileservice = {
  // Récupérer tous les établissements
  async getAll(location?: Location, filters?: FiltresRecherche): Promise<merchant[]> {
    try {
      console.log('🏪 Récupération des établissements...', { location, filters });

      let query = supabase
        .from('merchant_profiles')
        .select(`
          *,
          adresse:adresses(*),
          proprietaire:users(*)
        `)
        .eq('is_active', true);

      // Appliquer les filtres
      if (filters?.type_merchant?.length) {
        query = query.in('type_merchant', filters.type_merchant);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Erreur Supabase:', error);
        throw new Error(`Erreur de récupération des données: ${error.message}`);
      }

      console.log('✅ Établissements récupérés:', data?.length || 0);

      // Initialiser les données filtrées
      let filteredData = data || [];

      // Appliquer le filtre "ouvert maintenant" si activé
      if (filters?.ouvert_maintenant) {
        const now = new Date();
        const currentDay = now.toLocaleString('fr-FR', { weekday: 'long' }).toLowerCase();
        const currentTime = now.getHours() * 100 + now.getMinutes();

        filteredData = filteredData.filter(etab => {
          const horaires = etab.horaires_ouverture;
          if (!horaires || !horaires[currentDay]) return false;

          const { ouverture, fermeture } = horaires[currentDay];
          if (!ouverture || !fermeture) return false;

          const [openHour, openMinute] = ouverture.split(':').map(Number);
          const [closeHour, closeMinute] = fermeture.split(':').map(Number);

          const openTime = openHour * 100 + openMinute;
          const closeTime = closeHour * 100 + closeMinute;

          // Gestion des établissements ouverts après minuit
          if (closeTime < openTime) {
            return currentTime >= openTime || currentTime <= closeTime;
          }
          return currentTime >= openTime && currentTime <= closeTime;
        });
      }

      // Si location fournie, calculer les distances et filtrer
      if (location && filteredData.length > 0) {
        const merchant_profilesAvecDistance = filteredData.map(etab => ({
          ...etab,
          distance: utils.calculateDistance(
            location,
            etab.adresse.coordonnees
          )
        }));

        // Filtrer par distance si spécifié
        let filtered = merchant_profilesAvecDistance;
        if (filters?.distance_max) {
          filtered = filtered.filter(etab => etab.distance <= filters.distance_max!);
        }

        // Trier par distance
        return filtered.sort((a, b) => a.distance - b.distance);
      }

      return filteredData;
    } catch (error) {
      console.error('❌ Erreur dans getAll:', error);
      throw error instanceof Error ? error : new Error('Erreur de récupération des établissements');
    }
  },

  // Récupérer un établissement par ID
  async getById(id: string): Promise<merchant | null> {
    const { data, error } = await supabase
      .from('merchant_profiles')
      .select(`
        *,
        adresse:adresses(*),
        proprietaire:users(*),
        produits:produits(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Rechercher établissements
  async search(query: string, location?: Location): Promise<ResultatRecherche> {
    const { data: merchant_profiles, error: etabError } = await supabase
      .from('merchant_profiles')
      .select('*')
      .or(`nom.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true);

    const { data: produits, error: prodError } = await supabase
      .from('produits')
      .select(`
        *,
        merchant:merchant_profiles(*)
      `)
      .or(`nom.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_disponible', true);

    if (etabError) throw etabError;
    if (prodError) throw prodError;

    return {
      merchant_profiles: merchant_profiles || [],
      produits: produits || [],
      total: (merchant_profiles?.length || 0) + (produits?.length || 0),
      page: 1,
      pages_total: 1,
    };
  },
};

// Services pour les produits
export const produitService = {
  // Récupérer produits par établissement
  async getBymerchant(merchantId: string): Promise<Produit[]> {
    const { data, error } = await supabase
      .from('produits')
      .select('*')
      .eq('merchant_id', merchantId)
      .eq('is_disponible', true)
      .order('categorie', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Récupérer un produit par ID
  async getById(id: string): Promise<Produit | null> {
    const { data, error } = await supabase
      .from('produits')
      .select(`
        *,
        merchant:merchant_profiles(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },
};

// Services pour les commandes
export const commandeService = {
  // Créer une nouvelle commande
  async create(commandeData: Omit<Commande, 'id' | 'created_at' | 'updated_at'>): Promise<Commande> {
    const { data, error } = await supabase
      .from('commandes')
      .insert(commandeData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Récupérer commandes d'un utilisateur
  async getByUser(userId: string, role: string): Promise<Commande[]> {
    let query = supabase
      .from('commandes')
      .select(`
        *,
        items:items_commandes(*),
        merchant:merchant_profiles(*),
        client:users!client_id(*),
        livreur:users!livreur_id(*)
      `);

    switch (role) {
      case 'client':
        query = query.eq('client_id', userId);
        break;
      case 'livreur':
        query = query.eq('livreur_id', userId);
        break;
      case 'marchand':
        query = query.eq('merchant.proprietaire_id', userId);
        break;
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data || [];
  },

  // Mettre à jour le statut d'une commande
  async updateStatus(commandeId: string, statut: string): Promise<Commande> {
    const { data, error } = await supabase
      .from('commandes')
      .update({ statut, updated_at: new Date().toISOString() })
      .eq('id', commandeId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Récupérer commande par ID
  async getById(id: string): Promise<Commande | null> {
    const { data, error } = await supabase
      .from('commandes')
      .select(`
        *,
        items:items_commandes(*),
        merchant:merchant_profiles(*),
        client:users!client_id(*),
        livreur:users!livreur_id(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },
};

// Services pour les livraisons
export const livraisonService = {
  // Récupérer une livraison par ID
  async getById(id: string): Promise<Livraison | null> {
    const { data, error } = await supabase
      .from('livraisons')
      .select(`
        *,
        commande:commandes(*),
        livreur:users(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Récupérer livraisons disponibles pour un livreur
  async getAvailable(livreurLocation: Location): Promise<Livraison[]> {
    const { data, error } = await supabase
      .from('livraisons')
      .select(`
        *,
        commande:commandes(*),
        livreur:users(*)
      `)
      .eq('statut', 'assignee')
      .is('livreur_id', null);

    if (error) throw error;
    return data || [];
  },

  // Accepter une livraison
  async accept(livraisonId: string, livreurId: string): Promise<Livraison> {
    const { data, error } = await supabase
      .from('livraisons')
      .update({
        livreur_id: livreurId,
        statut: 'acceptee',
        heure_debut: new Date().toISOString(),
      })
      .eq('id', livraisonId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Mettre à jour la position du livreur
  async updateLocation(livraisonId: string, location: Location): Promise<void> {
    const { error } = await supabase
      .from('livraisons')
      .update({
        coordonnees_actuelles: location,
        updated_at: new Date().toISOString(),
      })
      .eq('id', livraisonId);

    if (error) throw error;
  },

  // Mettre à jour le statut de livraison
  async updateStatus(livraisonId: string, statut: string, data?: any): Promise<Livraison> {
    const updateData: any = {
      statut,
      updated_at: new Date().toISOString(),
    };

    if (statut === 'livree') {
      updateData.heure_fin = new Date().toISOString();
    }

    if (data) {
      Object.assign(updateData, data);
    }

    const { data: result, error } = await supabase
      .from('livraisons')
      .update(updateData)
      .eq('id', livraisonId)
      .select()
      .single();

    if (error) throw error;
    return result;
  },
};

// Services pour les adresses
export const adresseService = {
  // Récupérer adresses d'un utilisateur
  async getByUser(userId: string): Promise<Address[]> {
    const { data, error } = await supabase
      .from('adresses')
      .select('*')
      .eq('user_id', userId)
      .order('is_default', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Créer une nouvelle adresse
  async create(adresse: Omit<Address, 'id'>): Promise<Address> {
    // Si c'est l'adresse par défaut, mettre les autres à false
    if (adresse.is_default) {
      await supabase
        .from('adresses')
        .update({ is_default: false })
        .eq('user_id', adresse.user_id);
    }

    const { data, error } = await supabase
      .from('adresses')
      .insert(adresse)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Mettre à jour une adresse
  async update(id: string, updates: Partial<Address>): Promise<Address> {
    const { data, error } = await supabase
      .from('adresses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Supprimer une adresse
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('adresses')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Définir une adresse comme par défaut
  async setDefault(id: string): Promise<void> {
    // D'abord, récupérer l'adresse pour obtenir l'user_id
    const { data: adresse, error: fetchError } = await supabase
      .from('adresses')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    // Mettre toutes les adresses de l'utilisateur à false
    const { error: updateError } = await supabase
      .from('adresses')
      .update({ is_default: false })
      .eq('user_id', adresse.user_id);

    if (updateError) throw updateError;

    // Mettre l'adresse sélectionnée à true
    const { error: setError } = await supabase
      .from('adresses')
      .update({ is_default: true })
      .eq('id', id);

    if (setError) throw setError;
  },
};

// Services pour les notifications
export const notificationService = {
  // Récupérer notifications d'un utilisateur
  async getByUser(userId: string, unreadOnly: boolean = false): Promise<Notification[]> {
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId);

    if (unreadOnly) {
      query = query.eq('is_lu', false);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data || [];
  },

  // Marquer comme lu
  async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_lu: true })
      .eq('id', notificationId);

    if (error) throw error;
  },

  // Marquer toutes comme lues
  async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_lu: true })
      .eq('user_id', userId)
      .eq('is_lu', false);

    if (error) throw error;
  },

  // Créer une notification
  async create(notification: Omit<Notification, 'id' | 'created_at'>): Promise<Notification> {
    const { data, error } = await supabase
      .from('notifications')
      .insert({
        user_id: notification.user_id,
        titre: notification.titre,
        contenu: notification.contenu,
        type: notification.type,
        data_json: (notification as any).data || {},
        is_lu: false,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Supprimer une notification
  async delete(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) throw error;
  },

  // Compter les notifications non lues
  async getUnreadCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_lu', false);

    if (error) throw error;
    return count || 0;
  },

  // Récupérer notifications par type
  async getByType(userId: string, type: string): Promise<Notification[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .eq('type', type)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Envoyer notification push
  async sendPushNotification(userId: string, title: string, body: string, data?: any): Promise<void> {
    try {
      // Récupérer le token push de l'utilisateur
      const { data: tokens, error } = await supabase
        .from('push_tokens')
        .select('token')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;

      // Créer la notification en base
      await this.create({
        user_id: userId,
        titre: title,
        contenu: body,
        type: data?.type || 'system',
        data_json: data,
        is_read: false,
      });

      // Envoyer les push notifications (à implémenter avec Expo)
      if (tokens && tokens.length > 0) {
        console.log('Envoi de push notification vers:', tokens.map(t => t.token));
        // Ici, vous utiliseriez l'API Expo Push Notifications
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi de push notification:', error);
    }
  },
};

// Services pour le chat
export const chatService = {
  // Créer ou récupérer un chat pour une livraison
  async getOrCreateChatForDelivery(deliveryId: string): Promise<string> {
    // Vérifier si un chat existe déjà pour cette livraison
    const { data: existingMessages, error: checkError } = await supabase
      .from('chat_messages')
      .select('chat_id')
      .eq('delivery_id', deliveryId)
      .limit(1);

    if (checkError) throw checkError;

    if (existingMessages && existingMessages.length > 0) {
      return existingMessages[0].chat_id || deliveryId;
    }

    // Créer les participants du chat
    const { data: delivery, error: deliveryError } = await supabase
      .from('livraisons')
      .select(`
        client_id,
        livreur_id,
        commande:commandes(merchant_id)
      `)
      .eq('id', deliveryId)
      .single();

    if (deliveryError) throw deliveryError;

    const participants = [
      { delivery_id: deliveryId, user_id: delivery.client_id, role: 'client' },
    ];

    if (delivery.livreur_id) {
      participants.push({ delivery_id: deliveryId, user_id: delivery.livreur_id, role: 'livreur' });
    }

    if ((delivery as any).commande?.merchant_id) {
      participants.push({ delivery_id: deliveryId, user_id: (delivery as any).commande.merchant_id, role: 'marchand' });
    }

    // Insérer les participants
    const { error: participantsError } = await supabase
      .from('chat_participants')
      .insert(participants);

    if (participantsError) throw participantsError;

    return deliveryId; // Utiliser l'ID de livraison comme chat_id
  },

  // Envoyer un message
  async sendMessage(
    chatId: string,
    deliveryId: string,
    senderId: string,
    content: string,
    type: 'text' | 'image' | 'location' | 'system' = 'text',
    metadata?: any
  ): Promise<any> {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        chat_id: chatId,
        delivery_id: deliveryId,
        sender_id: senderId,
        content,
        type,
        metadata: metadata || {},
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Récupérer les messages d'un chat
  async getMessages(chatId: string, deliveryId: string, limit: number = 50): Promise<any[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        sender:users(id, full_name, role, avatar_url)
      `)
      .or(`chat_id.eq.${chatId},delivery_id.eq.${deliveryId}`)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) throw error;
    return data || [];
  },

  // Marquer les messages comme lus
  async markMessagesAsRead(chatId: string, deliveryId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .rpc('mark_messages_as_read', {
        chat_id: chatId,
        delivery_id: deliveryId,
        user_id: userId
      });

    if (error) throw error;
  },

  // Récupérer les participants d'un chat
  async getParticipants(chatId: string, deliveryId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('chat_participants')
      .select(`
        *,
        user:users(id, full_name, role, avatar_url, last_seen)
      `)
      .or(`chat_id.eq.${chatId},delivery_id.eq.${deliveryId}`);

    if (error) throw error;
    return data || [];
  },

  // Supprimer un message
  async deleteMessage(messageId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('id', messageId)
      .eq('sender_id', userId);

    if (error) throw error;
  },

  // Compter les messages non lus
  async getUnreadCount(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('id')
      .neq('sender_id', userId)
      .not('read_by', 'cs', `{${userId}}`);

    if (error) throw error;
    return data?.length || 0;
  },
};

// Services pour les tokens push
export const pushTokenService = {
  // Enregistrer un token push
  async register(userId: string, token: string, platform: 'ios' | 'android' | 'web'): Promise<void> {
    const { error } = await supabase
      .from('push_tokens')
      .upsert({
        user_id: userId,
        token,
        platform,
        is_active: true,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  },

  // Désactiver un token
  async deactivate(userId: string, token: string): Promise<void> {
    const { error } = await supabase
      .from('push_tokens')
      .update({ is_active: false })
      .eq('user_id', userId)
      .eq('token', token);

    if (error) throw error;
  },

  // Récupérer les tokens actifs d'un utilisateur
  async getActiveTokens(userId: string): Promise<string[]> {
    const { data, error } = await supabase
      .from('push_tokens')
      .select('token')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) throw error;
    return data?.map(t => t.token) || [];
  },
};

// Services pour l'historique de tracking
export const trackingService = {
  // Ajouter un événement de tracking
  async addEvent(
    deliveryId: string,
    status: string,
    location?: { latitude: number; longitude: number },
    message?: string
  ): Promise<void> {
    const { error } = await supabase
      .from('tracking_history')
      .insert({
        delivery_id: deliveryId,
        status,
        location: location ? { latitude: location.latitude, longitude: location.longitude } : null,
        message,
      });

    if (error) throw error;
  },

  // Récupérer l'historique de tracking
  async getHistory(deliveryId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('tracking_history')
      .select('*')
      .eq('delivery_id', deliveryId)
      .order('timestamp', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Mettre à jour la position du livreur
  async updateDeliveryLocation(
    deliveryId: string,
    latitude: number,
    longitude: number,
    estimatedArrival?: Date,
    distanceRemaining?: number
  ): Promise<void> {
    const updateData: any = {
      position_livreur_lat: latitude,
      position_livreur_lng: longitude,
      tracking_active: true,
      updated_at: new Date().toISOString(),
    };

    if (estimatedArrival) {
      updateData.estimated_arrival = estimatedArrival.toISOString();
    }

    if (distanceRemaining !== undefined) {
      updateData.distance_remaining = distanceRemaining;
    }

    const { error } = await supabase
      .from('livraisons')
      .update(updateData)
      .eq('id', deliveryId);

    if (error) throw error;

    // Ajouter à l'historique
    await this.addEvent(deliveryId, 'location_update', { latitude, longitude });
  },

  // Récupérer la position actuelle du livreur
  async getCurrentLocation(deliveryId: string): Promise<{ latitude: number; longitude: number } | null> {
    const { data, error } = await supabase
      .from('livraisons')
      .select('position_livreur_lat, position_livreur_lng')
      .eq('id', deliveryId)
      .single();

    if (error) throw error;

    if (data?.position_livreur_lat && data?.position_livreur_lng) {
      return {
        latitude: data.position_livreur_lat,
        longitude: data.position_livreur_lng,
      };
    }

    return null;
  },
};

// Services pour les statistiques
export const statistiqueService = {
  // Statistiques livreur
  async getLivreurStats(livreurId: string): Promise<StatistiquesLivreur> {
    // Utiliser les fonctions Supabase pour calculer les statistiques
    const { data, error } = await supabase
      .rpc('get_livreur_statistics', { livreur_id: livreurId });

    if (error) throw error;
    return data;
  },

  // Analytics marchand
  async getMarchandAnalytics(merchantId: string): Promise<AnalyticsMarchand> {
    const { data, error } = await supabase
      .rpc('get_marchand_analytics', { merchant_id: merchantId });

    if (error) throw error;
    return data;
  },

  // Nouvelles méthodes pour les analytics avancées
  async getOrderStats(startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('commandes')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    const total = data?.length || 0;
    const totalRevenue = data?.reduce((sum, order) => sum + (order.montant_total || 0), 0) || 0;
    const averageRating = data?.reduce((sum, order) => sum + (order.note_client || 0), 0) / total || 0;

    return {
      total,
      totalRevenue,
      average: total > 0 ? totalRevenue / total : 0,
      averageRating,
      daily: data?.filter(order =>
        new Date(order.created_at).toDateString() === new Date().toDateString()
      ).length || 0,
      weekly: data?.filter(order => {
        const orderDate = new Date(order.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return orderDate >= weekAgo;
      }).length || 0,
      monthly: data?.filter(order => {
        const orderDate = new Date(order.created_at);
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        return orderDate >= monthAgo;
      }).length || 0,
      cancellationRate: data?.filter(order => order.statut === 'annulee').length / total * 100 || 0,
    };
  },

  async getRevenueStats(startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('commandes')
      .select('montant_total, created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .neq('statut', 'annulee');

    if (error) throw error;

    const total = data?.reduce((sum, order) => sum + (order.montant_total || 0), 0) || 0;
    const count = data?.length || 0;

    return {
      total,
      average: count > 0 ? total / count : 0,
      count,
    };
  },

  async getDeliveryStats(startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('livraisons')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    const deliveries = data || [];
    const completedDeliveries = deliveries.filter(d => d.statut === 'livree');
    const onTimeDeliveries = completedDeliveries.filter(d => {
      if (!d.heure_fin || !d.heure_prevue) return false;
      return new Date(d.heure_fin) <= new Date(d.heure_prevue);
    });

    const averageTime = completedDeliveries.reduce((sum, d) => {
      if (!d.heure_debut || !d.heure_fin) return sum;
      const duration = new Date(d.heure_fin).getTime() - new Date(d.heure_debut).getTime();
      return sum + (duration / (1000 * 60)); // en minutes
    }, 0) / completedDeliveries.length || 0;

    return {
      total: deliveries.length,
      completed: completedDeliveries.length,
      averageTime,
      onTimeRate: completedDeliveries.length > 0 ? (onTimeDeliveries.length / completedDeliveries.length) * 100 : 0,
    };
  },

  async getPeriodMetrics(startDate: Date, endDate: Date, period: 'day' | 'week' | 'month' | 'year') {
    // Implémentation simplifiée - à améliorer selon les besoins
    const { data, error } = await supabase
      .from('commandes')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    // Grouper par période et retourner les métriques
    return data?.map(order => ({
      date: order.created_at.split('T')[0],
      orders: 1,
      revenue: order.montant_total || 0,
      averageDeliveryTime: 30, // À calculer réellement
      customerSatisfaction: order.note_client || 0,
    })) || [];
  },

  async getTopProducts(startDate: Date, endDate: Date, limit: number = 10) {
    const { data, error } = await supabase
      .from('items_commandes')
      .select(`
        produit_id,
        quantite,
        prix_unitaire,
        produit:produits(nom)
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    // Grouper par produit et calculer les métriques
    const productStats = new Map();
    data?.forEach(item => {
      const productId = item.produit_id;
      if (!productStats.has(productId)) {
        productStats.set(productId, {
          id: productId,
          name: (item as any).produit?.nom || 'Produit inconnu',
          orders: 0,
          revenue: 0,
          rating: 4.5, // À calculer réellement
        });
      }
      const stats = productStats.get(productId);
      stats.orders += item.quantite;
      stats.revenue += item.quantite * item.prix_unitaire;
    });

    return Array.from(productStats.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);
  },

  async getTopRestaurants(startDate: Date, endDate: Date, limit: number = 10) {
    const { data, error } = await supabase
      .from('commandes')
      .select(`
        merchant_id,
        montant_total,
        note_client,
        merchant:merchant_profiles(nom)
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    // Grouper par restaurant
    const restaurantStats = new Map();
    data?.forEach(order => {
      const merchantId = order.merchant_id;
      if (!restaurantStats.has(merchantId)) {
        restaurantStats.set(merchantId, {
          id: merchantId,
          name: (order as any).merchant?.nom || 'Restaurant inconnu',
          orders: 0,
          revenue: 0,
          rating: 0,
          ratingCount: 0,
          averagePreparationTime: 25, // À calculer réellement
        });
      }
      const stats = restaurantStats.get(merchantId);
      stats.orders += 1;
      stats.revenue += order.montant_total || 0;
      if (order.note_client) {
        stats.rating += order.note_client;
        stats.ratingCount += 1;
      }
    });

    return Array.from(restaurantStats.values())
      .map(stats => ({
        ...stats,
        rating: stats.ratingCount > 0 ? stats.rating / stats.ratingCount : 0,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);
  },

  async getTopDeliveryPersons(startDate: Date, endDate: Date, limit: number = 10) {
    const { data, error } = await supabase
      .from('livraisons')
      .select(`
        livreur_id,
        heure_debut,
        heure_fin,
        note_livreur,
        livreur:users(full_name)
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('statut', 'livree');

    if (error) throw error;

    // Grouper par livreur
    const deliveryPersonStats = new Map();
    data?.forEach(delivery => {
      const livreurId = delivery.livreur_id;
      if (!livreurId) return;

      if (!deliveryPersonStats.has(livreurId)) {
        deliveryPersonStats.set(livreurId, {
          id: livreurId,
          name: (delivery as any).livreur?.full_name || 'Livreur inconnu',
          deliveries: 0,
          rating: 0,
          ratingCount: 0,
          totalTime: 0,
          earnings: 0,
        });
      }
      const stats = deliveryPersonStats.get(livreurId);
      stats.deliveries += 1;
      if (delivery.note_livreur) {
        stats.rating += delivery.note_livreur;
        stats.ratingCount += 1;
      }
      if (delivery.heure_debut && delivery.heure_fin) {
        const duration = new Date(delivery.heure_fin).getTime() - new Date(delivery.heure_debut).getTime();
        stats.totalTime += duration / (1000 * 60); // en minutes
      }
      stats.earnings += 2000; // Commission fixe temporaire
    });

    return Array.from(deliveryPersonStats.values())
      .map(stats => ({
        ...stats,
        rating: stats.ratingCount > 0 ? stats.rating / stats.ratingCount : 0,
        averageDeliveryTime: stats.deliveries > 0 ? stats.totalTime / stats.deliveries : 0,
      }))
      .sort((a, b) => b.deliveries - a.deliveries)
      .slice(0, limit);
  },

  // Méthodes spécifiques par rôle
  async getMerchantOrderStats(merchantId: string, startDate: Date, endDate: Date) {
    const baseStats = await this.getOrderStats(startDate, endDate);

    // Ajouter les propriétés manquantes pour les marchands
    return {
      ...baseStats,
      averageDeliveryTime: 30, // À calculer réellement selon les livraisons du marchand
      onTimeRate: 85, // À calculer réellement selon les livraisons du marchand
    };
  },

  async getMerchantRevenueStats(merchantId: string, startDate: Date, endDate: Date) {
    return this.getRevenueStats(startDate, endDate);
  },

  async getMerchantProductStats(merchantId: string, startDate: Date, endDate: Date) {
    return this.getTopProducts(startDate, endDate, 10);
  },

  async getMerchantPeriodMetrics(merchantId: string, startDate: Date, endDate: Date, period: string) {
    return this.getPeriodMetrics(startDate, endDate, period as any);
  },

  async getDeliveryPersonStats(deliveryPersonId: string, startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('livraisons')
      .select('*')
      .eq('livreur_id', deliveryPersonId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    const deliveries = data || [];
    const completed = deliveries.filter(d => d.statut === 'livree');
    const cancelled = deliveries.filter(d => d.statut === 'annulee');

    return {
      totalDeliveries: deliveries.length,
      completed: completed.length,
      cancelled: cancelled.length,
      averageRating: completed.reduce((sum, d) => sum + (d.note_livreur || 0), 0) / completed.length || 0,
      averageTime: 30, // À calculer réellement
      onTimeRate: 85, // À calculer réellement
      cancellationRate: deliveries.length > 0 ? (cancelled.length / deliveries.length) * 100 : 0,
      daily: deliveries.filter(d =>
        new Date(d.created_at).toDateString() === new Date().toDateString()
      ).length,
      weekly: deliveries.filter(d => {
        const deliveryDate = new Date(d.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return deliveryDate >= weekAgo;
      }).length,
      monthly: deliveries.filter(d => {
        const deliveryDate = new Date(d.created_at);
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        return deliveryDate >= monthAgo;
      }).length,
    };
  },

  async getDeliveryPersonEarnings(deliveryPersonId: string, startDate: Date, endDate: Date) {
    const stats = await this.getDeliveryPersonStats(deliveryPersonId, startDate, endDate);
    const baseRate = 2000; // Commission par livraison

    return {
      total: stats.completed * baseRate,
      averagePerDelivery: baseRate,
      deliveries: stats.completed,
    };
  },

  async getDeliveryPersonPeriodMetrics(deliveryPersonId: string, startDate: Date, endDate: Date, period: string) {
    return this.getPeriodMetrics(startDate, endDate, period as any);
  },

  async getClientOrderStats(clientId: string, startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('commandes')
      .select('*')
      .eq('client_id', clientId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) throw error;

    const orders = data || [];
    const completed = orders.filter(o => o.statut === 'livree');

    return {
      total: orders.length,
      completed: completed.length,
      averageRating: completed.reduce((sum, o) => sum + (o.note_client || 0), 0) / completed.length || 0,
      averageDeliveryTime: 35, // À calculer réellement
      onTimeRate: 90, // À calculer réellement
      cancellationRate: orders.length > 0 ? (orders.filter(o => o.statut === 'annulee').length / orders.length) * 100 : 0,
      daily: orders.filter(o =>
        new Date(o.created_at).toDateString() === new Date().toDateString()
      ).length,
      weekly: orders.filter(o => {
        const orderDate = new Date(o.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return orderDate >= weekAgo;
      }).length,
      monthly: orders.filter(o => {
        const orderDate = new Date(o.created_at);
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        return orderDate >= monthAgo;
      }).length,
    };
  },

  async getClientSpendingStats(clientId: string, startDate: Date, endDate: Date) {
    const { data, error } = await supabase
      .from('commandes')
      .select('montant_total')
      .eq('client_id', clientId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .neq('statut', 'annulee');

    if (error) throw error;

    const orders = data || [];
    const total = orders.reduce((sum, o) => sum + (o.montant_total || 0), 0);

    return {
      total,
      average: orders.length > 0 ? total / orders.length : 0,
      orders: orders.length,
    };
  },

  async getClientFavoriteRestaurants(clientId: string, startDate: Date, endDate: Date) {
    return this.getTopRestaurants(startDate, endDate, 5);
  },

  async getClientFavoriteProducts(clientId: string, startDate: Date, endDate: Date) {
    return this.getTopProducts(startDate, endDate, 5);
  },
};

// Services pour les analytics
export const analyticsService = {
  // Enregistrer un événement analytics
  async trackEvent(
    userId: string | null,
    eventName: string,
    properties: Record<string, any> = {},
    sessionId?: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('analytics_events')
        .insert({
          user_id: userId,
          event_name: eventName,
          properties,
          session_id: sessionId || 'default',
        });

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'événement analytics:', error);
    }
  },

  // Récupérer les événements d'un utilisateur
  async getUserEvents(
    userId: string,
    startDate?: Date,
    endDate?: Date,
    eventName?: string
  ): Promise<any[]> {
    let query = supabase
      .from('analytics_events')
      .select('*')
      .eq('user_id', userId);

    if (startDate) {
      query = query.gte('timestamp', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('timestamp', endDate.toISOString());
    }

    if (eventName) {
      query = query.eq('event_name', eventName);
    }

    const { data, error } = await query.order('timestamp', { ascending: false });
    if (error) throw error;
    return data || [];
  },

  // Compter les événements par type
  async getEventCounts(
    startDate: Date,
    endDate: Date,
    userId?: string
  ): Promise<Record<string, number>> {
    let query = supabase
      .from('analytics_events')
      .select('event_name')
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;
    if (error) throw error;

    const counts: Record<string, number> = {};
    data?.forEach(event => {
      counts[event.event_name] = (counts[event.event_name] || 0) + 1;
    });

    return counts;
  },

  // Métriques de session
  async getSessionMetrics(sessionId: string): Promise<any> {
    const { data, error } = await supabase
      .from('analytics_events')
      .select('*')
      .eq('session_id', sessionId)
      .order('timestamp', { ascending: true });

    if (error) throw error;

    if (!data || data.length === 0) {
      return null;
    }

    const firstEvent = data[0];
    const lastEvent = data[data.length - 1];
    const duration = new Date(lastEvent.timestamp).getTime() - new Date(firstEvent.timestamp).getTime();

    return {
      sessionId,
      startTime: firstEvent.timestamp,
      endTime: lastEvent.timestamp,
      duration: duration / 1000, // en secondes
      eventCount: data.length,
      events: data,
    };
  },

  // Événements prédéfinis pour l'application
  async trackScreenView(userId: string | null, screenName: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'screen_view', { screen_name: screenName }, sessionId);
  },

  async trackOrderPlaced(userId: string, orderId: string, amount: number, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'order_placed', { order_id: orderId, amount }, sessionId);
  },

  async trackDeliveryStarted(userId: string, deliveryId: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'delivery_started', { delivery_id: deliveryId }, sessionId);
  },

  async trackDeliveryCompleted(userId: string, deliveryId: string, duration: number, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'delivery_completed', { delivery_id: deliveryId, duration }, sessionId);
  },

  async trackUserLogin(userId: string, method: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'user_login', { method }, sessionId);
  },

  async trackUserLogout(userId: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'user_logout', {}, sessionId);
  },

  async trackSearchPerformed(userId: string | null, query: string, results: number, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'search_performed', { query, results_count: results }, sessionId);
  },

  async trackProductViewed(userId: string | null, productId: string, productName: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'product_viewed', { product_id: productId, product_name: productName }, sessionId);
  },

  async trackRestaurantViewed(userId: string | null, restaurantId: string, restaurantName: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'restaurant_viewed', { restaurant_id: restaurantId, restaurant_name: restaurantName }, sessionId);
  },

  async trackCartUpdated(userId: string | null, action: 'add' | 'remove' | 'clear', productId?: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'cart_updated', { action, product_id: productId }, sessionId);
  },

  async trackPaymentAttempted(userId: string, orderId: string, method: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'payment_attempted', { order_id: orderId, payment_method: method }, sessionId);
  },

  async trackPaymentCompleted(userId: string, orderId: string, method: string, amount: number, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'payment_completed', { order_id: orderId, payment_method: method, amount }, sessionId);
  },

  async trackRatingGiven(userId: string, targetType: 'restaurant' | 'delivery' | 'product', targetId: string, rating: number, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'rating_given', { target_type: targetType, target_id: targetId, rating }, sessionId);
  },

  async trackNotificationReceived(userId: string, notificationType: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'notification_received', { notification_type: notificationType }, sessionId);
  },

  async trackNotificationOpened(userId: string, notificationId: string, notificationType: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'notification_opened', { notification_id: notificationId, notification_type: notificationType }, sessionId);
  },

  async trackChatMessageSent(userId: string, deliveryId: string, messageType: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'chat_message_sent', { delivery_id: deliveryId, message_type: messageType }, sessionId);
  },

  async trackLocationShared(userId: string, context: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'location_shared', { context }, sessionId);
  },

  async trackErrorOccurred(userId: string | null, errorType: string, errorMessage: string, sessionId?: string): Promise<void> {
    await this.trackEvent(userId, 'error_occurred', { error_type: errorType, error_message: errorMessage }, sessionId);
  },
};

// Utilitaires
export const utils = {
  // Calculer la distance entre deux points (en kilomètres)
  calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Rayon de la Terre en kilomètres
    const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  },

  // Upload d'image
  async uploadImage(uri: string, bucket: string, path: string): Promise<string> {
    const response = await fetch(uri);
    const blob = await response.blob();
    const arrayBuffer = await blob.arrayBuffer();

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, arrayBuffer, {
        contentType: blob.type,
        upsert: true,
      });

    if (error) throw error;

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);

    return publicUrl;
  },

  // Appeler Edge Function
  async callEdgeFunction(functionName: string, payload: any): Promise<any> {
    const { data, error } = await supabase.functions.invoke(functionName, {
      body: payload,
    });

    if (error) throw error;
    return data;
  },

  // Générer un ID de session unique
  generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  // Formater une date pour l'affichage
  formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {
    const d = typeof date === 'string' ? new Date(date) : date;

    switch (format) {
      case 'short':
        return d.toLocaleDateString('fr-FR');
      case 'long':
        return d.toLocaleDateString('fr-FR', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'time':
        return d.toLocaleTimeString('fr-FR', {
          hour: '2-digit',
          minute: '2-digit'
        });
      default:
        return d.toLocaleDateString('fr-FR');
    }
  },

  // Formater un montant en devise
  formatCurrency(amount: number, currency: string = 'XOF'): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  },

  // Valider un email
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Valider un numéro de téléphone
  isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^(\+225|0)[0-9]{8,10}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  },

  // Générer un code de vérification
  generateVerificationCode(length: number = 6): string {
    const digits = '0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += digits.charAt(Math.floor(Math.random() * digits.length));
    }
    return result;
  },

  // Calculer le temps estimé de livraison
  calculateEstimatedDeliveryTime(distance: number, trafficFactor: number = 1): number {
    // Vitesse moyenne en ville : 25 km/h
    const averageSpeed = 25;
    const baseTime = (distance / averageSpeed) * 60; // en minutes
    const preparationTime = 15; // temps de préparation en minutes

    return Math.round((baseTime * trafficFactor) + preparationTime);
  },

  // Générer une couleur basée sur un string
  generateColorFromString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    const hue = hash % 360;
    return `hsl(${hue}, 70%, 50%)`;
  },

  // Debounce function
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Throttle function
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
};

// Fonction utilitaire pour calculer la distance
const calculateDistance = (point1: Location, point2: Location): number => {
  const R = 6371; // Rayon de la Terre en kilomètres
  const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
  const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance en kilomètres
  return distance;
};

// Ajouter calculateDistance aux utils
utils.calculateDistance = calculateDistance;

export default supabase;
